"use strict";(()=>{var e={};e.id=809,e.ids=[809],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71371:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>u});var a=t(96559),o=t(48088),n=t(37719),d=t(32190),i=t(12909),p=t(31183);async function u(e){try{let{authenticated:r,user:t}=await (0,i.b9)(e);if(!r||!t)return d.NextResponse.json({error:"Not authenticated"},{status:401});let s=await p.z.depositAddress.findUnique({where:{userId:t.id}});if(!s){let e=function(){let e="**********************************************************",r="T";for(let t=0;t<33;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r}();s=await p.z.depositAddress.create({data:{userId:t.id,address:e,network:"TRC20",currency:"USDT"}})}return d.NextResponse.json({success:!0,data:{address:s.address,network:s.network,currency:s.currency}})}catch(e){return console.error("Deposit address error:",e),d.NextResponse.json({success:!1,error:"Failed to get deposit address"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/wallet/deposit-address/route",pathname:"/api/wallet/deposit-address",filename:"route",bundlePath:"app/api/wallet/deposit-address/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\deposit-address\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:h}=c;function w(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3529],()=>t(71371));module.exports=s})();