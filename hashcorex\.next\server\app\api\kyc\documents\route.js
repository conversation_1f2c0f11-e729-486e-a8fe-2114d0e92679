"use strict";(()=>{var e={};e.id=2298,e.ids=[2298],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53811:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(96559),n=t(48088),a=t(37719),u=t(32190),i=t(12909),c=t(31183);async function p(e){try{let{authenticated:r,user:t}=await (0,i.b9)(e);if(!r||!t)return u.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let s=await c.z.kYCDocument.findMany({where:{userId:t.id},orderBy:{createdAt:"desc"},select:{id:!0,documentType:!0,filePath:!0,status:!0,reviewedAt:!0,rejectionReason:!0,createdAt:!0}});return u.NextResponse.json({success:!0,data:s})}catch(e){return console.error("KYC documents fetch error:",e),u.NextResponse.json({success:!1,error:"Failed to fetch KYC documents"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/kyc/documents/route",pathname:"/api/kyc/documents",filename:"route",bundlePath:"app/api/kyc/documents/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\documents\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:l,serverHooks:m}=d;function h(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:l})}},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3529],()=>t(53811));module.exports=s})();