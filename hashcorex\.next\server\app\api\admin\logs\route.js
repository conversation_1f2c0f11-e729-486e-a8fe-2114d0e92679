"use strict";(()=>{var e={};e.id=3295,e.ids=[3295],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},95317:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>p});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),d=r(12909),u=r(31183);async function p(e){try{let{authenticated:t,user:r}=await (0,d.b9)(e);if(!t||!r)return i.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,d.qc)(r.id))return i.NextResponse.json({error:"Admin access required"},{status:403});let{searchParams:s}=new URL(e.url),a=parseInt(s.get("page")||"1"),n=parseInt(s.get("limit")||"50"),o=s.get("search")||"",p=s.get("action")||"all",c=s.get("dateRange")||"today",l={};o&&(l.OR=[{action:{contains:o,mode:"insensitive"}},{details:{contains:o,mode:"insensitive"}},{ipAddress:{contains:o,mode:"insensitive"}}]),"all"!==p&&(l.action=p);let g=new Date;switch(c){case"today":l.createdAt={gte:new Date(g.getFullYear(),g.getMonth(),g.getDate())};break;case"week":l.createdAt={gte:new Date(g.getTime()-6048e5)};break;case"month":l.createdAt={gte:new Date(g.getFullYear(),g.getMonth(),1)}}let[m,x]=await Promise.all([u.z.systemLog.findMany({where:l,include:{user:{select:{firstName:!0,lastName:!0,email:!0}}},orderBy:{createdAt:"desc"},skip:(a-1)*n,take:n}),u.z.systemLog.count({where:l})]),h=Math.ceil(x/n);return i.NextResponse.json({success:!0,data:{logs:m,totalPages:h}})}catch(e){return console.error("Admin logs fetch error:",e),i.NextResponse.json({success:!1,error:"Failed to fetch logs"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/logs/route",pathname:"/api/admin/logs",filename:"route",bundlePath:"app/api/admin/logs/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\logs\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:m}=c;function x(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3529],()=>r(95317));module.exports=s})();