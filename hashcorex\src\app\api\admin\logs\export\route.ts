import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);
    
    if (!authenticated || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const action = searchParams.get('action') || 'all';
    const dateRange = searchParams.get('dateRange') || 'today';

    // Build where clause
    const where: any = {};
    
    if (search) {
      where.OR = [
        { action: { contains: search, mode: 'insensitive' } },
        { details: { contains: search, mode: 'insensitive' } },
        { ipAddress: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (action !== 'all') {
      where.action = action;
    }

    // Date range filter
    const now = new Date();
    switch (dateRange) {
      case 'today':
        const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        where.createdAt = { gte: startOfDay };
        break;
      case 'week':
        const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        where.createdAt = { gte: startOfWeek };
        break;
      case 'month':
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        where.createdAt = { gte: startOfMonth };
        break;
      // 'all' - no date filter
    }

    // Get all logs (no pagination for export)
    const logs = await prisma.systemLog.findMany({
      where,
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Convert to CSV
    const csvHeaders = [
      'Date',
      'Action',
      'User',
      'Email',
      'Details',
      'IP Address',
      'User Agent',
    ];

    const csvRows = logs.map(log => [
      log.createdAt.toISOString(),
      log.action,
      log.user ? `${log.user.firstName} ${log.user.lastName}` : 'System',
      log.user?.email || 'N/A',
      log.details || '',
      log.ipAddress || '',
      log.userAgent || '',
    ]);

    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => 
        row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
      ),
    ].join('\n');

    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="system-logs-${new Date().toISOString().split('T')[0]}.csv"`,
      },
    });

  } catch (error) {
    console.error('Admin logs export error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to export logs' },
      { status: 500 }
    );
  }
}
