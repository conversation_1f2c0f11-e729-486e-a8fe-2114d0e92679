"use strict";(()=>{var e={};e.id=6843,e.ids=[6843],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72708:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>c});var a=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(12909),d=t(31183),p=t(6710);async function c(e){try{let r,{authenticated:t,user:s}=await (0,u.b9)(e);if(!t||!s)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,u.qc)(s.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let{userId:a,action:i}=await e.json();if(!a||!i)return o.NextResponse.json({error:"User ID and action are required"},{status:400});let n=await d.z.user.findUnique({where:{id:a},select:{id:!0,email:!0,firstName:!0,lastName:!0,role:!0,isActive:!0}});if(!n)return o.NextResponse.json({error:"User not found"},{status:404});let c="",l={};switch(i){case"activate":r=await d.z.user.update({where:{id:a},data:{isActive:!0}}),c="USER_ACTIVATED",l={targetUserId:a,targetUserEmail:n.email};break;case"deactivate":r=await d.z.user.update({where:{id:a},data:{isActive:!1}}),c="USER_DEACTIVATED",l={targetUserId:a,targetUserEmail:n.email};break;case"promote":if("ADMIN"===n.role)return o.NextResponse.json({error:"User is already an admin"},{status:400});r=await d.z.user.update({where:{id:a},data:{role:"ADMIN"}}),c="USER_PROMOTED_TO_ADMIN",l={targetUserId:a,targetUserEmail:n.email};break;case"demote":if("USER"===n.role)return o.NextResponse.json({error:"User is already a regular user"},{status:400});r=await d.z.user.update({where:{id:a},data:{role:"USER"}}),c="USER_DEMOTED_FROM_ADMIN",l={targetUserId:a,targetUserEmail:n.email};break;default:return o.NextResponse.json({error:"Invalid action"},{status:400})}return await p.AJ.create({action:c,userId:s.id,details:l,ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:`User ${i}d successfully`,data:r})}catch(e){return console.error("Admin user action error:",e),o.NextResponse.json({success:!1,error:"Failed to perform user action"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/users/action/route",pathname:"/api/admin/users/action",filename:"route",bundlePath:"app/api/admin/users/action/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\users\\action\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:g}=l;function w(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3529],()=>t(72708));module.exports=s})();