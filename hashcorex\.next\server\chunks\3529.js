exports.id=3529,exports.ids=[3529],exports.modules={6710:(a,e,t)=>{"use strict";t.d(e,{AJ:()=>o,DR:()=>n,FW:()=>l,Gy:()=>i,J6:()=>y,cc:()=>d,k_:()=>w,rs:()=>c,tg:()=>r,wJ:()=>u});var s=t(31183);let i={create:async a=>await s.z.user.create({data:{email:a.email,firstName:a.firstName,lastName:a.lastName,password:a.password,referralId:a.referralId||void 0}}),findByEmail:async a=>await s.z.user.findUnique({where:{email:a},include:{miningUnits:!0,transactions:!0,binaryPoints:!0}}),findById:async a=>await s.z.user.findUnique({where:{id:a},include:{miningUnits:!0,transactions:!0,binaryPoints:!0}}),findByReferralId:async a=>await s.z.user.findUnique({where:{referralId:a}}),update:async(a,e)=>await s.z.user.update({where:{id:a},data:e}),updateKYCStatus:async(a,e)=>await s.z.user.update({where:{id:a},data:{kycStatus:e}})},r={async create(a){let e=new Date;return e.setFullYear(e.getFullYear()+1),await s.z.miningUnit.create({data:{userId:a.userId,thsAmount:a.thsAmount,investmentAmount:a.investmentAmount,dailyROI:a.dailyROI,expiryDate:e}})},findActiveByUserId:async a=>await s.z.miningUnit.findMany({where:{userId:a,status:"ACTIVE",expiryDate:{gt:new Date}}}),updateTotalEarned:async(a,e)=>await s.z.miningUnit.update({where:{id:a},data:{totalEarned:{increment:e}}}),expireUnit:async a=>await s.z.miningUnit.update({where:{id:a},data:{status:"EXPIRED"}})},n={create:async a=>await s.z.transaction.create({data:{userId:a.userId,type:a.type,amount:a.amount,description:a.description,status:a.status||"PENDING"}}),async findByUserId(a,e){let t={userId:a};return e?.types&&e.types.length>0&&(t.type={in:e.types}),e?.status&&(t.status=e.status),await s.z.transaction.findMany({where:t,orderBy:{createdAt:"desc"},take:e?.limit||50,skip:e?.offset})},updateStatus:async(a,e)=>await s.z.transaction.update({where:{id:a},data:{status:e}})},d={create:async a=>await s.z.referral.create({data:{referrerId:a.referrerId,referredId:a.referredId,placementSide:a.placementSide}}),findByReferrerId:async a=>await s.z.referral.findMany({where:{referrerId:a},include:{referred:{select:{id:!0,email:!0,createdAt:!0}}}})},l={upsert:async a=>await s.z.binaryPoints.upsert({where:{userId:a.userId},update:{leftPoints:void 0!==a.leftPoints?{increment:a.leftPoints}:void 0,rightPoints:void 0!==a.rightPoints?{increment:a.rightPoints}:void 0},create:{userId:a.userId,leftPoints:a.leftPoints||0,rightPoints:a.rightPoints||0}}),findByUserId:async a=>await s.z.binaryPoints.findUnique({where:{userId:a}}),resetPoints:async(a,e,t)=>await s.z.binaryPoints.update({where:{userId:a},data:{leftPoints:e,rightPoints:t,flushDate:new Date}})},u={create:async a=>await s.z.withdrawalRequest.create({data:{userId:a.userId,amount:a.amount,usdtAddress:a.usdtAddress}}),findPending:async()=>await s.z.withdrawalRequest.findMany({where:{status:"PENDING"},include:{user:{select:{id:!0,email:!0,kycStatus:!0}}},orderBy:{createdAt:"asc"}}),updateStatus:async(a,e,t,i,r)=>await s.z.withdrawalRequest.update({where:{id:a},data:{status:e,processedBy:t,txid:i,rejectionReason:r,processedAt:new Date}})},c={async get(a){let e=await s.z.adminSettings.findUnique({where:{key:a}});return e?.value},set:async(a,e,t)=>await s.z.adminSettings.upsert({where:{key:a},update:{value:e,updatedBy:t},create:{key:a,value:e,updatedBy:t}}),getAll:async()=>await s.z.adminSettings.findMany()},o={create:async a=>await s.z.systemLog.create({data:{action:a.action,userId:a.userId,adminId:a.adminId,details:a.details?JSON.stringify(a.details):null,ipAddress:a.ipAddress,userAgent:a.userAgent}})},w={async getOrCreate(a){let e=await s.z.walletBalance.findUnique({where:{userId:a}});return e||(e=await s.z.walletBalance.create({data:{userId:a,availableBalance:0,pendingBalance:0,totalDeposits:0,totalWithdrawals:0,totalEarnings:0}})),e},updateBalance:async(a,e)=>await s.z.walletBalance.update({where:{userId:a},data:{...e,lastUpdated:new Date}}),async addDeposit(a,e){let t=await this.getOrCreate(a);return await s.z.walletBalance.update({where:{userId:a},data:{availableBalance:t.availableBalance+e,totalDeposits:t.totalDeposits+e,lastUpdated:new Date}})},async addEarnings(a,e){let t=await this.getOrCreate(a);return await s.z.walletBalance.update({where:{userId:a},data:{availableBalance:t.availableBalance+e,totalEarnings:t.totalEarnings+e,lastUpdated:new Date}})},async deductWithdrawal(a,e){let t=await this.getOrCreate(a);if(t.availableBalance<e)throw Error("Insufficient balance");return await s.z.walletBalance.update({where:{userId:a},data:{availableBalance:t.availableBalance-e,totalWithdrawals:t.totalWithdrawals+e,lastUpdated:new Date}})},async findByUserId(a){return await this.getOrCreate(a)}},y={create:async a=>await s.z.depositTransaction.create({data:{userId:a.userId,transactionId:a.transactionId,amount:a.amount,usdtAmount:a.usdtAmount,tronAddress:a.tronAddress,senderAddress:a.senderAddress,blockNumber:a.blockNumber,blockTimestamp:a.blockTimestamp,confirmations:a.confirmations||0,status:"PENDING"}}),findByTransactionId:async a=>await s.z.depositTransaction.findUnique({where:{transactionId:a},include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}}),async findByUserId(a,e){let t={userId:a};return e?.status&&(t.status=e.status),await s.z.depositTransaction.findMany({where:t,orderBy:{createdAt:"desc"},take:e?.limit||50,skip:e?.offset,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}})},async findAll(a){let e={};return a?.status&&(e.status=a.status),await s.z.depositTransaction.findMany({where:e,orderBy:{createdAt:"desc"},take:a?.limit||100,skip:a?.offset,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}})},async updateStatus(a,e,t){let i={status:e};return t?.verifiedAt&&(i.verifiedAt=t.verifiedAt),t?.processedAt&&(i.processedAt=t.processedAt),t?.failureReason&&(i.failureReason=t.failureReason),t?.confirmations!==void 0&&(i.confirmations=t.confirmations),await s.z.depositTransaction.update({where:{transactionId:a},data:i})},async markAsCompleted(a){return await this.updateStatus(a,"COMPLETED",{processedAt:new Date})},async markAsFailed(a,e){return await this.updateStatus(a,"FAILED",{failureReason:e,processedAt:new Date})},async getPendingDeposits(){return await this.findAll({status:"PENDING"})},async getDepositStats(){let a=await s.z.depositTransaction.aggregate({_count:{id:!0},_sum:{usdtAmount:!0},where:{status:"COMPLETED"}}),e=await s.z.depositTransaction.count({where:{status:"PENDING"}});return{totalDeposits:a._count.id||0,totalAmount:a._sum.usdtAmount||0,pendingDeposits:e}}}},12909:(a,e,t)=>{"use strict";t.d(e,{DT:()=>I,DY:()=>p,Lx:()=>f,Oj:()=>h,b9:()=>m,qc:()=>A});var s=t(85663),i=t(43205),r=t.n(i),n=t(6710);let d=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"7d",u=async a=>await s.Ay.hash(a,12),c=async(a,e)=>await s.Ay.compare(a,e),o=a=>r().sign(a,d,{expiresIn:l}),w=a=>{try{return r().verify(a,d)}catch(a){return null}},y=()=>{let a="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",e="HC";for(let t=0;t<8;t++)e+=a.charAt(Math.floor(Math.random()*a.length));return e},m=async a=>{let e=a.headers.get("authorization")?.replace("Bearer ","")||a.cookies.get("auth-token")?.value;if(!e)return{authenticated:!1,user:null};let t=w(e);if(!t)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(t.email);return s&&s.isActive?{authenticated:!0,user:s}:{authenticated:!1,user:null}},p=async a=>{let e,s;if(await n.Gy.findByEmail(a.email))throw Error("User already exists with this email");if(a.referralCode){let t=await n.Gy.findByReferralId(a.referralCode);if(!t)throw Error("Invalid referral code");e=t.id}let i=await u(a.password),r=!1;do s=y(),r=!await n.Gy.findByReferralId(s);while(!r);let d=await n.Gy.create({email:a.email,firstName:a.firstName,lastName:a.lastName,password:i,referralId:s});if(e){let{placeUserInBinaryTree:s,placeUserInSpecificSide:i}=await t.e(2746).then(t.bind(t,2746));a.placementSide?await i(e,d.id,a.placementSide.toUpperCase()):await s(e,d.id)}return{id:d.id,email:d.email,referralId:d.referralId,kycStatus:d.kycStatus}},f=async a=>{let e=await n.Gy.findByEmail(a.email);if(!e)throw Error("Invalid email or password");if(!e.isActive)throw Error("Account is deactivated");if(!await c(a.password,e.password))throw Error("Invalid email or password");return{token:o({userId:e.id,email:e.email}),user:{id:e.id,email:e.email,referralId:e.referralId,kycStatus:e.kycStatus}}},h=a=>{let e=[];return a.length<8&&e.push("Password must be at least 8 characters long"),/[A-Z]/.test(a)||e.push("Password must contain at least one uppercase letter"),/[a-z]/.test(a)||e.push("Password must contain at least one lowercase letter"),/\d/.test(a)||e.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(a)||e.push("Password must contain at least one special character"),{valid:0===e.length,errors:e}},I=a=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a),A=async a=>{let e=await n.Gy.findById(a);return e?.role==="ADMIN"}},31183:(a,e,t)=>{"use strict";t.d(e,{z:()=>i});var s=t(96330);let i=globalThis.prisma??new s.PrismaClient},78335:()=>{},96487:()=>{}};