(()=>{var e={};e.id=6303,e.ids=[6303],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,a)=>{"use strict";a.d(t,{AJ:()=>c,DR:()=>i,FW:()=>d,Gy:()=>r,J6:()=>w,cc:()=>o,k_:()=>p,rs:()=>l,tg:()=>s,wJ:()=>u});var n=a(31183);let r={create:async e=>await n.z.user.create({data:{email:e.email,firstName:e.firstName,lastName:e.lastName,password:e.password,referralId:e.referralId||void 0}}),findByEmail:async e=>await n.z.user.findUnique({where:{email:e},include:{miningUnits:!0,transactions:!0,binaryPoints:!0}}),findById:async e=>await n.z.user.findUnique({where:{id:e},include:{miningUnits:!0,transactions:!0,binaryPoints:!0}}),findByReferralId:async e=>await n.z.user.findUnique({where:{referralId:e}}),update:async(e,t)=>await n.z.user.update({where:{id:e},data:t}),updateKYCStatus:async(e,t)=>await n.z.user.update({where:{id:e},data:{kycStatus:t}})},s={async create(e){let t=new Date;return t.setFullYear(t.getFullYear()+1),await n.z.miningUnit.create({data:{userId:e.userId,thsAmount:e.thsAmount,investmentAmount:e.investmentAmount,dailyROI:e.dailyROI,expiryDate:t}})},findActiveByUserId:async e=>await n.z.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}}}),updateTotalEarned:async(e,t)=>await n.z.miningUnit.update({where:{id:e},data:{totalEarned:{increment:t}}}),expireUnit:async e=>await n.z.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}})},i={create:async e=>await n.z.transaction.create({data:{userId:e.userId,type:e.type,amount:e.amount,description:e.description,status:e.status||"PENDING"}}),async findByUserId(e,t){let a={userId:e};return t?.types&&t.types.length>0&&(a.type={in:t.types}),t?.status&&(a.status=t.status),await n.z.transaction.findMany({where:a,orderBy:{createdAt:"desc"},take:t?.limit||50,skip:t?.offset})},updateStatus:async(e,t)=>await n.z.transaction.update({where:{id:e},data:{status:t}})},o={create:async e=>await n.z.referral.create({data:{referrerId:e.referrerId,referredId:e.referredId,placementSide:e.placementSide}}),findByReferrerId:async e=>await n.z.referral.findMany({where:{referrerId:e},include:{referred:{select:{id:!0,email:!0,createdAt:!0}}}})},d={upsert:async e=>await n.z.binaryPoints.upsert({where:{userId:e.userId},update:{leftPoints:void 0!==e.leftPoints?{increment:e.leftPoints}:void 0,rightPoints:void 0!==e.rightPoints?{increment:e.rightPoints}:void 0},create:{userId:e.userId,leftPoints:e.leftPoints||0,rightPoints:e.rightPoints||0}}),findByUserId:async e=>await n.z.binaryPoints.findUnique({where:{userId:e}}),resetPoints:async(e,t,a)=>await n.z.binaryPoints.update({where:{userId:e},data:{leftPoints:t,rightPoints:a,flushDate:new Date}})},u={create:async e=>await n.z.withdrawalRequest.create({data:{userId:e.userId,amount:e.amount,usdtAddress:e.usdtAddress}}),findPending:async()=>await n.z.withdrawalRequest.findMany({where:{status:"PENDING"},include:{user:{select:{id:!0,email:!0,kycStatus:!0}}},orderBy:{createdAt:"asc"}}),updateStatus:async(e,t,a,r,s)=>await n.z.withdrawalRequest.update({where:{id:e},data:{status:t,processedBy:a,txid:r,rejectionReason:s,processedAt:new Date}})},l={async get(e){let t=await n.z.adminSettings.findUnique({where:{key:e}});return t?.value},set:async(e,t,a)=>await n.z.adminSettings.upsert({where:{key:e},update:{value:t,updatedBy:a},create:{key:e,value:t,updatedBy:a}}),getAll:async()=>await n.z.adminSettings.findMany()},c={create:async e=>await n.z.systemLog.create({data:{action:e.action,userId:e.userId,adminId:e.adminId,details:e.details?JSON.stringify(e.details):null,ipAddress:e.ipAddress,userAgent:e.userAgent}})},p={async getOrCreate(e){let t=await n.z.walletBalance.findUnique({where:{userId:e}});return t||(t=await n.z.walletBalance.create({data:{userId:e,availableBalance:0,pendingBalance:0,totalDeposits:0,totalWithdrawals:0,totalEarnings:0}})),t},updateBalance:async(e,t)=>await n.z.walletBalance.update({where:{userId:e},data:{...t,lastUpdated:new Date}}),async addDeposit(e,t){let a=await this.getOrCreate(e);return await n.z.walletBalance.update({where:{userId:e},data:{availableBalance:a.availableBalance+t,totalDeposits:a.totalDeposits+t,lastUpdated:new Date}})},async addEarnings(e,t){let a=await this.getOrCreate(e);return await n.z.walletBalance.update({where:{userId:e},data:{availableBalance:a.availableBalance+t,totalEarnings:a.totalEarnings+t,lastUpdated:new Date}})},async deductWithdrawal(e,t){let a=await this.getOrCreate(e);if(a.availableBalance<t)throw Error("Insufficient balance");return await n.z.walletBalance.update({where:{userId:e},data:{availableBalance:a.availableBalance-t,totalWithdrawals:a.totalWithdrawals+t,lastUpdated:new Date}})},async findByUserId(e){return await this.getOrCreate(e)}},w={create:async e=>await n.z.depositTransaction.create({data:{userId:e.userId,transactionId:e.transactionId,amount:e.amount,usdtAmount:e.usdtAmount,tronAddress:e.tronAddress,senderAddress:e.senderAddress,blockNumber:e.blockNumber,blockTimestamp:e.blockTimestamp,confirmations:e.confirmations||0,status:"PENDING"}}),findByTransactionId:async e=>await n.z.depositTransaction.findUnique({where:{transactionId:e},include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}}),async findByUserId(e,t){let a={userId:e};return t?.status&&(a.status=t.status),await n.z.depositTransaction.findMany({where:a,orderBy:{createdAt:"desc"},take:t?.limit||50,skip:t?.offset,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}})},async findAll(e){let t={};return e?.status&&(t.status=e.status),await n.z.depositTransaction.findMany({where:t,orderBy:{createdAt:"desc"},take:e?.limit||100,skip:e?.offset,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}})},async updateStatus(e,t,a){let r={status:t};return a?.verifiedAt&&(r.verifiedAt=a.verifiedAt),a?.processedAt&&(r.processedAt=a.processedAt),a?.failureReason&&(r.failureReason=a.failureReason),a?.confirmations!==void 0&&(r.confirmations=a.confirmations),await n.z.depositTransaction.update({where:{transactionId:e},data:r})},async markAsCompleted(e){return await this.updateStatus(e,"COMPLETED",{processedAt:new Date})},async markAsFailed(e,t){return await this.updateStatus(e,"FAILED",{failureReason:t,processedAt:new Date})},async getPendingDeposits(){return await this.findAll({status:"PENDING"})},async getDepositStats(){let e=await n.z.depositTransaction.aggregate({_count:{id:!0},_sum:{usdtAmount:!0},where:{status:"COMPLETED"}}),t=await n.z.depositTransaction.count({where:{status:"PENDING"}});return{totalDeposits:e._count.id||0,totalAmount:e._sum.usdtAmount||0,pendingDeposits:t}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,a)=>{"use strict";a.d(t,{z:()=>r});var n=a(96330);let r=globalThis.prisma??new n.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77158:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>y,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>w});var n={};a.r(n),a.d(n,{POST:()=>l});var r=a(96559),s=a(48088),i=a(37719),o=a(32190),d=a(92731),u=a(6710);async function l(e){try{let t=e.headers.get("authorization"),a=process.env.CRON_SECRET||"default-secret";if(t!==`Bearer ${a}`)return o.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});console.log("Starting weekly earnings payout cron job...");let n=await (0,d.Oh)();console.log(`Processed earnings for ${n.length} users`);let r=n.reduce((e,t)=>e+t.totalEarnings,0);return await u.AJ.create({action:"WEEKLY_PAYOUT_CRON_EXECUTED",details:{usersProcessed:n.length,totalDistributed:r,executionTime:new Date().toISOString(),payoutDay:"Saturday",payoutTime:"15:00 UTC"}}),o.NextResponse.json({success:!0,message:"Weekly earnings payout completed",data:{usersProcessed:n.length,totalDistributed:r}})}catch(e){return console.error("Weekly payout cron job error:",e),await u.AJ.create({action:"WEEKLY_PAYOUT_CRON_ERROR",details:{error:e.message,stack:e.stack,timestamp:new Date().toISOString()}}),o.NextResponse.json({success:!1,error:"Weekly payout failed"},{status:500})}}let c=new r.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/cron/weekly-payout/route",pathname:"/api/cron/weekly-payout",filename:"route",bundlePath:"app/api/cron/weekly-payout/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\weekly-payout\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:p,workUnitAsyncStorage:w,serverHooks:y}=c;function m(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:w})}},78335:()=>{},92731:(e,t,a)=>{"use strict";a.d(t,{IT:()=>d,Oh:()=>i,WL:()=>s,eB:()=>o});var n=a(31183),r=a(6710);async function s(){try{console.log("Starting daily ROI calculation...");let e=await n.z.miningUnit.findMany({where:{status:"ACTIVE",expiryDate:{gt:new Date}},include:{user:!0}});console.log(`Found ${e.length} active mining units`);let t=[];for(let a of e)try{let e=a.investmentAmount*a.dailyROI/100,n=a.totalEarned+e,s=5*a.investmentAmount,i=e,o=!1;n>=s&&(i=s-a.totalEarned,o=!0),i>0&&(await r.tg.updateTotalEarned(a.id,i),await r.DR.create({userId:a.userId,type:"MINING_EARNINGS",amount:i,description:`Daily mining earnings - ${a.thsAmount} TH/s`,status:"PENDING"}),t.push({unitId:a.id,userId:a.userId,earnings:i,expired:o})),o&&(await r.tg.expireUnit(a.id),await r.AJ.create({action:"MINING_UNIT_EXPIRED",userId:a.userId,details:{miningUnitId:a.id,reason:"5x_investment_reached",totalEarned:n,investmentAmount:a.investmentAmount}}))}catch(e){console.error(`Error processing unit ${a.id}:`,e)}return await r.AJ.create({action:"DAILY_ROI_CALCULATED",details:{unitsProcessed:e.length,totalEarnings:t.reduce((e,t)=>e+t.earnings,0),expiredUnits:t.filter(e=>e.expired).length,timestamp:new Date().toISOString()}}),console.log(`Daily ROI calculation completed. Processed ${t.length} units.`),t}catch(e){throw console.error("Daily ROI calculation error:",e),e}}async function i(){try{console.log("Starting weekly earnings distribution...");let e=await n.z.transaction.findMany({where:{type:"MINING_EARNINGS",status:"PENDING"},include:{user:!0}});console.log(`Found ${e.length} pending earnings transactions`);let t=new Map;for(let a of e){let e=t.get(a.userId)||0;t.set(a.userId,e+a.amount)}let a=[];for(let[e,r]of t)try{await n.z.transaction.updateMany({where:{userId:e,type:"MINING_EARNINGS",status:"PENDING"},data:{status:"COMPLETED"}}),a.push({userId:e,totalEarnings:r})}catch(t){console.error(`Error processing earnings for user ${e}:`,t)}return await r.AJ.create({action:"WEEKLY_EARNINGS_DISTRIBUTED",details:{usersProcessed:a.length,totalDistributed:a.reduce((e,t)=>e+t.totalEarnings,0),transactionsProcessed:e.length,timestamp:new Date().toISOString()}}),console.log(`Weekly earnings distribution completed. Processed ${a.length} users.`),a}catch(e){throw console.error("Weekly earnings distribution error:",e),e}}async function o(){try{console.log("Checking for expired mining units...");let e=await n.z.miningUnit.findMany({where:{status:"ACTIVE",expiryDate:{lte:new Date}}});for(let t of(console.log(`Found ${e.length} units to expire`),e))await r.tg.expireUnit(t.id),await r.AJ.create({action:"MINING_UNIT_EXPIRED",userId:t.userId,details:{miningUnitId:t.id,reason:"12_months_reached",totalEarned:t.totalEarned,investmentAmount:t.investmentAmount}});return e.length}catch(e){throw console.error("Mining unit expiry check error:",e),e}}async function d(e){try{let t=await r.tg.findActiveByUserId(e);if(0===t.length)return{next7Days:0,next30Days:0,next365Days:0};let a=0;for(let e of t){let t=e.investmentAmount*e.dailyROI/100,n=5*e.investmentAmount-e.totalEarned;a+=Math.min(t,n)}return{next7Days:7*a,next30Days:30*a,next365Days:365*a}}catch(e){throw console.error("Estimated earnings calculation error:",e),e}}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[4447,580],()=>a(77158));module.exports=n})();