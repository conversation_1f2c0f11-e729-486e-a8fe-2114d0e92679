'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui';
import { MiningRig } from '@/components/icons';
import { Calendar, TrendingUp, DollarSign } from 'lucide-react';
import { formatCurrency, formatTHS, formatDate, formatNumber } from '@/lib/utils';

interface MiningUnit {
  id: string;
  thsAmount: number;
  investmentAmount: number;
  startDate: string;
  expiryDate: string;
  dailyROI: number;
  totalEarned: number;
  status: 'ACTIVE' | 'EXPIRED';
  createdAt: string;
}

export const MiningUnitsTable: React.FC = () => {
  const [miningUnits, setMiningUnits] = useState<MiningUnit[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchMiningUnits();
  }, []);

  const fetchMiningUnits = async () => {
    try {
      const response = await fetch('/api/mining-units', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setMiningUnits(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch mining units:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateProgress = (unit: MiningUnit) => {
    const maxEarnings = unit.investmentAmount * 5;
    const progress = (unit.totalEarned / maxEarnings) * 100;
    return Math.min(progress, 100);
  };

  const getDaysRemaining = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(diffDays, 0);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-eco-100 text-eco-700';
      case 'EXPIRED':
        return 'bg-gray-100 text-gray-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MiningRig className="h-5 w-5 text-solar-500" />
            <span>Mining Units</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MiningRig className="h-5 w-5 text-solar-500" />
          <span>Mining Units ({miningUnits.length})</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {miningUnits.length > 0 ? (
          <div className="space-y-4">
            {/* Desktop Table */}
            <div className="hidden md:block overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Mining Power</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Investment</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Daily ROI</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Total Earned</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Progress</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Expires</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {miningUnits.map((unit) => (
                    <tr key={unit.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          <MiningRig className="h-4 w-4 text-solar-500" />
                          <span className="font-medium">{formatTHS(unit.thsAmount)}</span>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <span className="font-medium">{formatCurrency(unit.investmentAmount)}</span>
                      </td>
                      <td className="py-4 px-4">
                        <span className="text-eco-600 font-medium">
                          {formatNumber(unit.dailyROI, 2)}%
                        </span>
                      </td>
                      <td className="py-4 px-4">
                        <div>
                          <span className="font-medium text-eco-600">
                            {formatCurrency(unit.totalEarned)}
                          </span>
                          <div className="text-xs text-gray-500">
                            / {formatCurrency(unit.investmentAmount * 5)} max
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-eco-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${calculateProgress(unit)}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {formatNumber(calculateProgress(unit), 1)}%
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div>
                          <span className="text-sm">{formatDate(unit.expiryDate)}</span>
                          <div className="text-xs text-gray-500">
                            {getDaysRemaining(unit.expiryDate)} days left
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(unit.status)}`}>
                          {unit.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div className="md:hidden space-y-4">
              {miningUnits.map((unit) => (
                <div key={unit.id} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <MiningRig className="h-5 w-5 text-solar-500" />
                      <span className="font-semibold">{formatTHS(unit.thsAmount)}</span>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(unit.status)}`}>
                      {unit.status}
                    </span>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-3">
                    <div>
                      <div className="text-xs text-gray-500">Investment</div>
                      <div className="font-medium">{formatCurrency(unit.investmentAmount)}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Daily ROI</div>
                      <div className="font-medium text-eco-600">{formatNumber(unit.dailyROI, 2)}%</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Total Earned</div>
                      <div className="font-medium text-eco-600">{formatCurrency(unit.totalEarned)}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Days Left</div>
                      <div className="font-medium">{getDaysRemaining(unit.expiryDate)}</div>
                    </div>
                  </div>

                  <div className="mb-2">
                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                      <span>Progress to 5x</span>
                      <span>{formatNumber(calculateProgress(unit), 1)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-eco-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${calculateProgress(unit)}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="text-xs text-gray-500">
                    Started: {formatDate(unit.startDate)} • Expires: {formatDate(unit.expiryDate)}
                  </div>
                </div>
              ))}
            </div>

            {/* Summary */}
            <div className="mt-6 p-4 bg-solar-50 rounded-lg">
              <h4 className="font-medium text-dark-900 mb-2">Summary</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Total Mining Power: </span>
                  <span className="font-medium">
                    {formatTHS(miningUnits.reduce((sum, unit) => sum + unit.thsAmount, 0))}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Total Investment: </span>
                  <span className="font-medium">
                    {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.investmentAmount, 0))}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Total Earned: </span>
                  <span className="font-medium text-eco-600">
                    {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.totalEarned, 0))}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <MiningRig className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Mining Units</h3>
            <p className="text-gray-500 mb-4">
              You haven't purchased any mining units yet. Start mining to earn daily returns!
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
