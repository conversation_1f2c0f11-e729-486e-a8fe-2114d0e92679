(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={1132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13090:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ea});var a=t(60687),l=t(43210),r=t(57445),i=t(16189),n=t(85814),c=t.n(n),d=t(36679),x=t(71180),o=t(49625),m=t(41312),h=t(99891),u=t(23928),p=t(85778),j=t(84027),b=t(62688);let N=(0,b.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var f=t(11860),g=t(28559),v=t(40083),w=t(12941),y=t(97051),A=t(78272),k=t(58869);let C=({children:e,activeTab:s,onTabChange:t})=>{let{user:i,logout:n}=(0,r.A)(),[b,C]=(0,l.useState)(!1),[S,R]=(0,l.useState)(!1),E=(0,l.useRef)(null),P=[{id:"dashboard",label:"Dashboard",icon:o.A},{id:"users",label:"User Management",icon:m.A},{id:"kyc",label:"KYC Review",icon:h.A},{id:"deposits",label:"Deposits",icon:u.A},{id:"withdrawals",label:"Withdrawals",icon:p.A},{id:"settings",label:"System Settings",icon:j.A},{id:"logs",label:"System Logs",icon:N}],D=async()=>{await n()};return(0,l.useEffect)(()=>{let e=e=>{E.current&&!E.current.contains(e.target)&&R(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,a.jsxs)("div",{className:"min-h-screen bg-slate-900 flex",children:[b&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-75 lg:hidden",onClick:()=>C(!1)}),(0,a.jsx)("aside",{className:`
        fixed inset-y-0 left-0 z-50 w-64 bg-slate-800 shadow-xl border-r border-slate-700
        transform transition-all duration-300 ease-in-out
        ${b?"translate-x-0":"-translate-x-full lg:translate-x-0"}
      `,children:(0,a.jsxs)("div",{className:"flex flex-col h-screen",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-14 px-5 border-b border-slate-700 bg-slate-800 flex-shrink-0",children:[(0,a.jsxs)(c(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(x.MX,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"text-lg font-bold text-white",children:"HashCoreX"})]}),(0,a.jsx)("button",{onClick:()=>C(!1),className:"lg:hidden p-1.5 rounded-lg text-slate-400 hover:text-white hover:bg-slate-700 transition-colors",children:(0,a.jsx)(f.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"px-3 py-3 bg-red-600 border-b border-slate-700",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-white",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-semibold",children:"Admin Panel"})]})}),(0,a.jsx)("nav",{className:"flex-1 px-3 py-4 space-y-1 min-h-0",children:P.map(e=>{let l=e.icon,r=s===e.id;return(0,a.jsxs)("button",{onClick:()=>{t(e.id),C(!1)},className:`
                    w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 group
                    ${r?"bg-blue-600 text-white shadow-md":"text-slate-300 hover:bg-slate-700 hover:text-white"}
                  `,children:[(0,a.jsx)(l,{className:`h-4 w-4 ${r?"text-white":"text-slate-400 group-hover:text-white"}`}),(0,a.jsx)("span",{className:"font-medium text-sm",children:e.label})]},e.id)})}),(0,a.jsx)("div",{className:"px-3 py-3 border-t border-slate-700",children:(0,a.jsxs)(c(),{href:"/dashboard",className:"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 hover:bg-orange-600 hover:text-white transition-all duration-200 group",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 group-hover:text-white"}),(0,a.jsx)("span",{className:"font-medium text-sm",children:"Back to Dashboard"})]})}),(0,a.jsx)("div",{className:"px-3 py-3 border-t border-slate-700 bg-slate-900 flex-shrink-0",children:(0,a.jsxs)("button",{onClick:D,className:"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 hover:bg-red-600 hover:text-white transition-all duration-200 group",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 group-hover:text-white"}),(0,a.jsx)("span",{className:"font-medium text-sm",children:"Logout"})]})})]})}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col min-w-0 lg:ml-64",children:[(0,a.jsx)("header",{className:"bg-slate-800 shadow-sm border-b border-slate-700 sticky top-0 z-30",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 xl:px-12",children:(0,a.jsxs)(d.so,{justify:"between",align:"center",className:"h-16",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>C(!0),className:"lg:hidden p-2 rounded-lg text-slate-400 hover:text-white hover:bg-slate-700 transition-colors",children:(0,a.jsx)(w.A,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-white capitalize",children:P.find(e=>e.id===s)?.label||"Admin Dashboard"}),(0,a.jsx)("p",{className:"text-sm text-slate-400 hidden sm:block",children:"Manage platform operations and user activities"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("button",{className:"relative p-2 rounded-lg text-slate-400 hover:text-white hover:bg-slate-700 transition-colors",children:[(0,a.jsx)(y.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"absolute top-1 right-1 h-2 w-2 bg-orange-500 rounded-full"})]}),(0,a.jsx)("div",{className:"px-3 py-1.5 rounded-lg text-xs font-semibold border bg-red-600 text-white border-red-500",children:"ADMIN"}),(0,a.jsxs)("div",{className:"relative",ref:E,children:[(0,a.jsxs)("button",{onClick:()=>R(!S),className:"flex items-center space-x-2 p-1 rounded-lg hover:bg-slate-700 transition-colors",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-semibold text-sm",children:i?.firstName?.charAt(0).toUpperCase()||i?.email.charAt(0).toUpperCase()})}),(0,a.jsx)(A.A,{className:`h-4 w-4 text-slate-400 transition-transform ${S?"rotate-180":""}`})]}),S&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-slate-800 rounded-xl shadow-lg border border-slate-700 py-2 z-50",children:[(0,a.jsx)("div",{className:"px-4 py-3 border-b border-slate-700",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold",children:i?.firstName?.charAt(0).toUpperCase()||i?.email.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-semibold text-white truncate",children:i?.firstName&&i?.lastName?`${i.firstName} ${i.lastName}`:i?.email.split("@")[0]}),(0,a.jsxs)("p",{className:"text-xs text-slate-400",children:["ID: ",i?.referralId]}),(0,a.jsx)("p",{className:"text-xs text-red-400 font-medium",children:"Administrator"})]})]})}),(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)(c(),{href:"/dashboard",onClick:()=>R(!1),className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-slate-300 hover:bg-slate-700 transition-colors",children:[(0,a.jsx)(k.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"User Dashboard"})]}),(0,a.jsx)("div",{className:"border-t border-slate-700 my-1"}),(0,a.jsxs)("button",{onClick:()=>{R(!1),D()},className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-400 hover:bg-red-600 hover:text-white transition-colors",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Sign Out"})]})]})]})]})]})]})})}),(0,a.jsx)("main",{className:"flex-1 bg-slate-900 overflow-y-auto",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 xl:px-12 py-6",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto",children:e})})})]})]})};var S=t(61212),R=t(5336),E=t(25541),P=t(45583),D=t(43649),I=t(4780);let T=()=>{let[e,s]=(0,l.useState)(null),[t,r]=(0,l.useState)(!0);(0,l.useEffect)(()=>{i()},[]);let i=async()=>{try{let e=await fetch("/api/admin/stats",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("Failed to fetch admin stats:",e)}finally{r(!1)}};return t?(0,a.jsx)("div",{className:"space-y-6",children:Array.from({length:4}).map((e,s)=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsx)("div",{className:"h-32 bg-slate-700 rounded-xl"})},s))}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-slate-800 border border-slate-700 rounded-xl p-6 text-white",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-2",children:"Admin Dashboard"}),(0,a.jsx)("p",{className:"text-slate-300",children:"Monitor platform performance, manage users, and oversee all operations."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"User Management"}),(0,a.jsxs)(d.xA,{cols:{default:1,md:2,lg:4},gap:6,children:[(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,I.ZV)(e.totalUsers,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(m.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Active Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,I.ZV)(e.activeUsers,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(R.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Pending KYC"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-400",children:(0,I.ZV)(e.pendingKYC,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Approved KYC"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,I.ZV)(e.approvedKYC,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-white"})})]})})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Financial Overview"}),(0,a.jsxs)(d.xA,{cols:{default:1,md:2,lg:4},gap:6,children:[(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Total Investments"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,I.vv)(e.totalInvestments)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Earnings Distributed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,I.vv)(e.totalEarningsDistributed)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(E.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Platform Revenue"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-400",children:(0,I.vv)(e.platformRevenue)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Binary Pool"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-400",children:(0,I.vv)(e.binaryPoolBalance)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-red-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(E.A,{className:"h-6 w-6 text-white"})})]})})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Mining Operations"}),(0,a.jsxs)(d.xA,{cols:{default:1,md:2},gap:6,children:[(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Total TH/s Sold"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-400",children:(0,I.jI)(e.totalTHSSold)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(P.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Active TH/s"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,I.jI)(e.activeTHS)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(P.A,{className:"h-6 w-6 text-white"})})]})})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Withdrawal Management"}),(0,a.jsxs)(d.xA,{cols:{default:1,md:2},gap:6,children:[(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Pending Withdrawals"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-400",children:(0,I.ZV)(e.pendingWithdrawals,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-red-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(D.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Total Withdrawals"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,I.vv)(e.totalWithdrawals)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(p.A,{className:"h-6 w-6 text-white"})})]})})})]})]}),(0,a.jsxs)(S.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(S.aR,{children:(0,a.jsx)(S.ZB,{className:"text-white",children:"Quick Actions"})}),(0,a.jsx)(S.Wu,{children:(0,a.jsxs)(d.xA,{cols:{default:1,md:3},gap:4,children:[(0,a.jsx)("div",{className:"p-4 border border-slate-600 rounded-lg hover:bg-slate-700 cursor-pointer transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-orange-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white",children:"Review KYC"}),(0,a.jsxs)("p",{className:"text-sm text-slate-400",children:[e.pendingKYC," pending reviews"]})]})]})}),(0,a.jsx)("div",{className:"p-4 border border-slate-600 rounded-lg hover:bg-slate-700 cursor-pointer transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(p.A,{className:"h-8 w-8 text-red-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white",children:"Process Withdrawals"}),(0,a.jsxs)("p",{className:"text-sm text-slate-400",children:[e.pendingWithdrawals," pending"]})]})]})}),(0,a.jsx)("div",{className:"p-4 border border-slate-600 rounded-lg hover:bg-slate-700 cursor-pointer transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 text-blue-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white",children:"Manage Users"}),(0,a.jsxs)("p",{className:"text-sm text-slate-400",children:[e.totalUsers," total users"]})]})]})})]})})]})]}):(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-slate-400",children:"Failed to load admin statistics"})})})},U=(0,b.A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),M=(0,b.A)("shield-x",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m14.5 9.5-5 5",key:"17q4r4"}],["path",{d:"m9.5 9.5 5 5",key:"18nt4w"}]]);var W=t(99270);let F=(0,b.A)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]),Z=(0,b.A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),_=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(""),[c,d]=(0,l.useState)("all"),[x,o]=(0,l.useState)(1),[u,p]=(0,l.useState)(1);(0,l.useEffect)(()=>{j()},[x,i,c]);let j=async()=>{try{r(!0);let e=new URLSearchParams({page:x.toString(),limit:"20",search:i,status:c}),t=await fetch(`/api/admin/users?${e}`,{credentials:"include"});if(t.ok){let e=await t.json();e.success&&(s(e.data.users),p(e.data.totalPages))}}catch(e){console.error("Failed to fetch users:",e)}finally{r(!1)}},b=async(e,s)=>{try{(await fetch("/api/admin/users/action",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({userId:e,action:s})})).ok&&j()}catch(e){console.error("Failed to perform user action:",e)}},N=e=>{switch(e){case"APPROVED":return(0,a.jsx)(U,{className:"h-4 w-4 text-green-400"});case"REJECTED":return(0,a.jsx)(M,{className:"h-4 w-4 text-red-400"});default:return(0,a.jsx)(h.A,{className:"h-4 w-4 text-yellow-400"})}},f=e=>(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${e?"bg-blue-600 text-white":"bg-red-600 text-white"}`,children:e?"Active":"Inactive"}),g=e=>(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"ADMIN"===e?"bg-red-600 text-white":"bg-blue-600 text-white"}`,children:e});return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"User Management"}),(0,a.jsx)("p",{className:"text-slate-400 mt-1",children:"Manage platform users and their permissions"})]})}),(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(W.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{placeholder:"Search users by email or name...",value:i,onChange:e=>n(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"})]})}),(0,a.jsx)("div",{className:"sm:w-48",children:(0,a.jsxs)("select",{value:c,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Users"}),(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"}),(0,a.jsx)("option",{value:"pending_kyc",children:"Pending KYC"})]})})]})})}),(0,a.jsxs)(S.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(S.aR,{children:(0,a.jsxs)(S.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Users (",e.length,")"]})}),(0,a.jsxs)(S.Wu,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-slate-600",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"User"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"Role"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"KYC Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"Joined"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:e.map(e=>(0,a.jsxs)("tr",{className:"border-b border-slate-700 hover:bg-slate-700",children:[(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium text-white",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:e.email}),(0,a.jsxs)("div",{className:"text-xs text-slate-500",children:["ID: ",e.referralId]})]})}),(0,a.jsx)("td",{className:"py-4 px-4",children:g(e.role)}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[N(e.kycStatus),(0,a.jsx)("span",{className:"text-sm text-slate-300",children:e.kycStatus})]})}),(0,a.jsx)("td",{className:"py-4 px-4",children:f(e.isActive)}),(0,a.jsx)("td",{className:"py-4 px-4 text-sm text-slate-400",children:(0,I.Yq)(e.createdAt)}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(S.$n,{variant:"outline",size:"sm",onClick:()=>b(e.id,e.isActive?"deactivate":"activate"),className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:e.isActive?(0,a.jsx)(F,{className:"h-4 w-4"}):(0,a.jsx)(Z,{className:"h-4 w-4"})}),"USER"===e.role&&(0,a.jsx)(S.$n,{variant:"outline",size:"sm",onClick:()=>b(e.id,"promote"),title:"Promote to Admin",className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})]})})]},e.id))})]})}),u>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between mt-6",children:[(0,a.jsxs)("div",{className:"text-sm text-slate-400",children:["Page ",x," of ",u]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(S.$n,{variant:"outline",size:"sm",onClick:()=>o(e=>Math.max(1,e-1)),disabled:1===x,className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:"Previous"}),(0,a.jsx)(S.$n,{variant:"outline",size:"sm",onClick:()=>o(e=>Math.min(u,e+1)),disabled:x===u,className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:"Next"})]})]})]})]})]})};var $=t(40228),L=t(13861),O=t(13964);let q=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(null),[c,d]=(0,l.useState)(null),[x,o]=(0,l.useState)(""),[m,u]=(0,l.useState)(!1);(0,l.useEffect)(()=>{p()},[]);let p=async()=>{try{r(!0);let e=await fetch("/api/admin/kyc/pending",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("Failed to fetch KYC documents:",e)}finally{r(!1)}},j=async(e,s,t)=>{try{u(!0),(await fetch("/api/admin/kyc/review",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({documentId:e,action:s.toUpperCase(),rejectionReason:t})})).ok&&(p(),n(null),d(null),o(""))}catch(e){console.error("Failed to review KYC document:",e)}finally{u(!1)}},b=e=>(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{PENDING:"bg-yellow-900 text-yellow-300 border border-yellow-700",APPROVED:"bg-green-900 text-green-300 border border-green-700",REJECTED:"bg-red-900 text-red-300 border border-red-700"}[e]}`,children:e});return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"KYC Review"}),(0,a.jsx)("p",{className:"text-slate-400 mt-1",children:"Review and approve user KYC documents"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-slate-400",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 text-orange-400"}),e.length," pending reviews"]})]}),(0,a.jsx)("div",{className:"grid gap-6",children:0===e.length?(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsxs)(S.Wu,{className:"p-12 text-center",children:[(0,a.jsx)(h.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No Pending KYC Reviews"}),(0,a.jsx)("p",{className:"text-slate-400",children:"All KYC documents have been reviewed."})]})}):e.map(e=>(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700 hover:bg-slate-750 transition-colors",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(k.A,{className:"h-5 w-5 text-slate-400"}),(0,a.jsxs)("span",{className:"font-medium text-white",children:[e.user.firstName," ",e.user.lastName]})]}),b(e.status)]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-slate-400 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Email:"})," ",e.user.email]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"User ID:"})," ",e.user.referralId]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)($.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Submitted:"})," ",(0,I.Yq)(e.submittedAt)]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-slate-400 mb-4",children:[(0,a.jsx)(N,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Document Type:"})," ",e.documentType]}),e.rejectionReason&&(0,a.jsxs)("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-3 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-red-300 text-sm font-medium mb-1",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),"Rejection Reason"]}),(0,a.jsx)("p",{className:"text-red-400 text-sm",children:e.rejectionReason})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[(0,a.jsxs)(S.$n,{variant:"outline",size:"sm",onClick:()=>window.open(e.documentUrl,"_blank"),className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white",children:[(0,a.jsx)(L.A,{className:"h-4 w-4 mr-1"}),"View"]}),"PENDING"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(S.$n,{variant:"outline",size:"sm",onClick:()=>{n(e),d("approve")},className:"border-green-600 text-green-400 hover:text-green-300 hover:bg-green-900/20",children:[(0,a.jsx)(O.A,{className:"h-4 w-4 mr-1"}),"Approve"]}),(0,a.jsxs)(S.$n,{variant:"outline",size:"sm",onClick:()=>{n(e),d("reject")},className:"border-red-600 text-red-400 hover:text-red-300 hover:bg-red-900/20",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-1"}),"Reject"]})]})]})]})})},e.id))}),i&&c&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-slate-800 border border-slate-700 rounded-xl max-w-md w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4 text-white",children:"approve"===c?"Approve KYC Document":"Reject KYC Document"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("p",{className:"text-slate-400 mb-2",children:["User: ",(0,a.jsxs)("span",{className:"font-medium text-white",children:[i.user.firstName," ",i.user.lastName]})]}),(0,a.jsxs)("p",{className:"text-slate-400",children:["Document: ",(0,a.jsx)("span",{className:"font-medium text-white",children:i.documentType})]})]}),"reject"===c&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Rejection Reason *"}),(0,a.jsx)("textarea",{value:x,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent",rows:3,placeholder:"Please provide a reason for rejection...",required:!0})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)(S.$n,{variant:"outline",onClick:()=>{n(null),d(null),o("")},disabled:m,className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white",children:"Cancel"}),(0,a.jsx)(S.$n,{onClick:()=>j(i.id,c,"reject"===c?x:void 0),disabled:m||"reject"===c&&!x.trim(),loading:m,className:"approve"===c?"bg-green-600 hover:bg-green-700 text-white":"bg-red-600 hover:bg-red-700 text-white",children:"approve"===c?"Approve":"Reject"})]})]})})]})};var Y=t(48730),z=t(35071),H=t(78122);let G=(0,b.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),K=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)({totalDeposits:0,totalAmount:0,pendingDeposits:0}),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(null),[x,o]=(0,l.useState)("ALL"),[m,h]=(0,l.useState)(null),[p,j]=(0,l.useState)(null),b=async()=>{try{n(!0);let e=new URLSearchParams;"ALL"!==x&&e.append("status",x),e.append("limit","50");let t=await fetch(`/api/admin/deposits?${e}`,{headers:{Authorization:`Bearer ${localStorage.getItem("auth-token")}`}});if(!t.ok)throw Error("Failed to fetch deposits");let a=await t.json();if(a.success)s(a.data.deposits),r(a.data.stats);else throw Error(a.error||"Failed to fetch deposits")}catch(e){d(e instanceof Error?e.message:"An error occurred")}finally{n(!1)}},N=async(e,s,t)=>{try{j(e);let a=await fetch("/api/admin/deposits",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth-token")}`},body:JSON.stringify({action:s,transactionId:e,reason:t})});if(!a.ok)throw Error("Failed to process deposit action");let l=await a.json();if(l.success)await b(),h(null);else throw Error(l.error||"Failed to process deposit action")}catch(e){d(e instanceof Error?e.message:"An error occurred")}finally{j(null)}};(0,l.useEffect)(()=>{b()},[x]);let f=e=>{switch(e){case"COMPLETED":return(0,a.jsx)(R.A,{className:"w-4 h-4 text-green-400"});case"PENDING":return(0,a.jsx)(Y.A,{className:"w-4 h-4 text-yellow-400"});case"FAILED":case"REJECTED":return(0,a.jsx)(z.A,{className:"w-4 h-4 text-red-400"});case"VERIFYING":return(0,a.jsx)(D.A,{className:"w-4 h-4 text-blue-400"});default:return(0,a.jsx)(Y.A,{className:"w-4 h-4 text-gray-400"})}},g=e=>{switch(e){case"COMPLETED":return"text-green-400 bg-green-400/10";case"PENDING":return"text-yellow-400 bg-yellow-400/10";case"FAILED":case"REJECTED":return"text-red-400 bg-red-400/10";case"VERIFYING":return"text-blue-400 bg-blue-400/10";default:return"text-gray-400 bg-gray-400/10"}};return i?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)(H.A,{className:"w-8 h-8 animate-spin text-blue-400"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Deposit Management"}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("button",{onClick:b,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:[(0,a.jsx)(H.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Refresh"})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)(S.Zp,{className:"bg-gray-800 border-gray-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Deposits"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:t.totalDeposits})]}),(0,a.jsx)(u.A,{className:"w-8 h-8 text-green-400"})]})})}),(0,a.jsx)(S.Zp,{className:"bg-gray-800 border-gray-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Amount"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,I.vv)(t.totalAmount)})]}),(0,a.jsx)(u.A,{className:"w-8 h-8 text-blue-400"})]})})}),(0,a.jsx)(S.Zp,{className:"bg-gray-800 border-gray-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Pending Deposits"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:t.pendingDeposits})]}),(0,a.jsx)(Y.A,{className:"w-8 h-8 text-yellow-400"})]})})})]}),(0,a.jsx)(S.Zp,{className:"bg-gray-800 border-gray-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(G,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("select",{value:x,onChange:e=>o(e.target.value),className:"bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"ALL",children:"All Status"}),(0,a.jsx)("option",{value:"PENDING",children:"Pending"}),(0,a.jsx)("option",{value:"VERIFYING",children:"Verifying"}),(0,a.jsx)("option",{value:"CONFIRMED",children:"Confirmed"}),(0,a.jsx)("option",{value:"COMPLETED",children:"Completed"}),(0,a.jsx)("option",{value:"FAILED",children:"Failed"}),(0,a.jsx)("option",{value:"REJECTED",children:"Rejected"})]})]})})}),(0,a.jsxs)(S.Zp,{className:"bg-gray-800 border-gray-700",children:[(0,a.jsx)(S.aR,{children:(0,a.jsx)(S.ZB,{className:"text-white",children:"Recent Deposits"})}),(0,a.jsxs)(S.Wu,{children:[c&&(0,a.jsx)("div",{className:"mb-4 p-4 bg-red-900/20 border border-red-500 rounded-lg",children:(0,a.jsx)("p",{className:"text-red-400",children:c})}),(0,a.jsxs)("div",{className:"overflow-x-auto",children:[(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-700",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"User"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Amount"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Transaction ID"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Date"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:e.map(e=>(0,a.jsxs)("tr",{className:"border-b border-gray-700/50 hover:bg-gray-700/30",children:[(0,a.jsxs)("td",{className:"py-3 px-4",children:[(0,a.jsx)("div",{className:"text-white",children:e.user?`${e.user.firstName} ${e.user.lastName}`:"Unknown"}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:e.user?.email||"No email"})]}),(0,a.jsxs)("td",{className:"py-3 px-4",children:[(0,a.jsxs)("div",{className:"text-white font-medium",children:[(0,I.vv)(e.usdtAmount)," USDT"]}),(0,a.jsxs)("div",{className:"text-sm text-gray-400",children:[e.confirmations," confirmations"]})]}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:`inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ${g(e.status)}`,children:[f(e.status),(0,a.jsx)("span",{children:e.status})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"text-white font-mono text-sm",children:[e.transactionId.slice(0,8),"...",e.transactionId.slice(-8)]})}),(0,a.jsx)("td",{className:"py-3 px-4 text-gray-300",children:(0,I.Yq)(e.createdAt)}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("button",{onClick:()=>h(e),className:"flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors",children:[(0,a.jsx)(L.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"View"})]})})]},e.id))})]}),0===e.length&&(0,a.jsx)("div",{className:"text-center py-8 text-gray-400",children:"No deposits found"})]})]})]}),m&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:"Deposit Details"}),(0,a.jsx)("button",{onClick:()=>h(null),className:"text-gray-400 hover:text-white",children:(0,a.jsx)(z.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"User"}),(0,a.jsx)("p",{className:"text-white",children:m.user?`${m.user.firstName} ${m.user.lastName}`:"Unknown"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:m.user?.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Amount"}),(0,a.jsxs)("p",{className:"text-white font-medium",children:[(0,I.vv)(m.usdtAmount)," USDT"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Status"}),(0,a.jsxs)("div",{className:`inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ${g(m.status)}`,children:[f(m.status),(0,a.jsx)("span",{children:m.status})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Confirmations"}),(0,a.jsx)("p",{className:"text-white",children:m.confirmations})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Transaction ID"}),(0,a.jsx)("p",{className:"text-white font-mono text-sm break-all",children:m.transactionId})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Sender Address"}),(0,a.jsx)("p",{className:"text-white font-mono text-sm break-all",children:m.senderAddress||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Deposit Address"}),(0,a.jsx)("p",{className:"text-white font-mono text-sm break-all",children:m.tronAddress})]})]}),m.failureReason&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Failure Reason"}),(0,a.jsx)("p",{className:"text-red-400",children:m.failureReason})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Created At"}),(0,a.jsx)("p",{className:"text-white",children:(0,I.Yq)(m.createdAt)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Processed At"}),(0,a.jsx)("p",{className:"text-white",children:m.processedAt?(0,I.Yq)(m.processedAt):"Not processed"})]})]})]}),("PENDING"===m.status||"VERIFYING"===m.status)&&(0,a.jsxs)("div",{className:"flex space-x-4 mt-6 pt-6 border-t border-gray-700",children:[(0,a.jsx)("button",{onClick:()=>N(m.transactionId,"approve"),disabled:p===m.transactionId,className:"flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors disabled:opacity-50",children:p===m.transactionId?"Processing...":"Approve"}),(0,a.jsx)("button",{onClick:()=>{let e=prompt("Enter rejection reason:");e&&N(m.transactionId,"reject",e)},disabled:p===m.transactionId,className:"flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50",children:"Reject"})]})]})})]})},B=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(""),[c,d]=(0,l.useState)("all"),[x,o]=(0,l.useState)(null),[m,h]=(0,l.useState)(null),[j,b]=(0,l.useState)(""),[N,g]=(0,l.useState)(""),[v,w]=(0,l.useState)(!1);(0,l.useEffect)(()=>{y()},[i,c]);let y=async()=>{try{r(!0);let e=new URLSearchParams({search:i,status:c}),t=await fetch(`/api/admin/withdrawals?${e}`,{credentials:"include"});if(t.ok){let e=await t.json();e.success&&s(e.data)}}catch(e){console.error("Failed to fetch withdrawals:",e)}finally{r(!1)}},A=async(e,s,t)=>{try{w(!0),(await fetch("/api/admin/withdrawals/action",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({withdrawalId:e,action:s.toUpperCase(),...t})})).ok&&(y(),o(null),h(null),b(""),g(""))}catch(e){console.error("Failed to process withdrawal action:",e)}finally{w(!1)}},C=e=>{let s={PENDING:{color:"bg-yellow-900 text-yellow-300 border border-yellow-700",icon:Y.A},APPROVED:{color:"bg-blue-900 text-blue-300 border border-blue-700",icon:R.A},REJECTED:{color:"bg-red-900 text-red-300 border border-red-700",icon:z.A},COMPLETED:{color:"bg-green-900 text-green-300 border border-green-700",icon:R.A}}[e],t=s.icon;return(0,a.jsxs)("span",{className:`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${s.color}`,children:[(0,a.jsx)(t,{className:"h-3 w-3"}),e]})};return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Withdrawal Management"}),(0,a.jsx)("p",{className:"text-slate-400 mt-1",children:"Review and process user withdrawal requests"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-sm text-slate-400",children:"Pending Amount"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:(0,I.vv)(e.filter(e=>"PENDING"===e.status).reduce((e,s)=>e+s.amount,0))})]})]}),(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(W.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{placeholder:"Search by user email or wallet address...",value:i,onChange:e=>n(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})}),(0,a.jsx)("div",{className:"sm:w-48",children:(0,a.jsxs)("select",{value:c,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Withdrawals"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"approved",children:"Approved"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"rejected",children:"Rejected"})]})})]})})}),(0,a.jsx)("div",{className:"grid gap-4",children:0===e.length?(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsxs)(S.Wu,{className:"p-12 text-center",children:[(0,a.jsx)(p.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No Withdrawal Requests"}),(0,a.jsx)("p",{className:"text-slate-400",children:"No withdrawal requests match your current filters."})]})}):e.map(e=>(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700 hover:bg-slate-750 transition-colors",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(k.A,{className:"h-5 w-5 text-slate-400"}),(0,a.jsxs)("span",{className:"font-medium text-white",children:[e.user.firstName," ",e.user.lastName]})]}),C(e.status)]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-slate-400 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Email:"})," ",e.user.email]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"User ID:"})," ",e.user.referralId]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Amount:"})," ",(0,I.vv)(e.amount)]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)($.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Requested:"})," ",(0,I.Yq)(e.requestedAt)]})]}),(0,a.jsxs)("div",{className:"text-sm text-slate-400 mb-4",children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Wallet Address:"}),(0,a.jsx)("div",{className:"font-mono text-xs bg-slate-700 border border-slate-600 p-2 rounded mt-1 break-all text-slate-300",children:e.walletAddress})]}),e.transactionHash&&(0,a.jsxs)("div",{className:"text-sm text-slate-400 mb-4",children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Transaction Hash:"}),(0,a.jsx)("div",{className:"font-mono text-xs bg-green-900/20 border border-green-700 p-2 rounded mt-1 break-all text-green-300",children:e.transactionHash})]}),e.rejectionReason&&(0,a.jsxs)("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-3 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-red-300 text-sm font-medium mb-1",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),"Rejection Reason"]}),(0,a.jsx)("p",{className:"text-red-400 text-sm",children:e.rejectionReason})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:["PENDING"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(S.$n,{variant:"outline",size:"sm",onClick:()=>{o(e),h("approve")},className:"border-green-600 text-green-400 hover:text-green-300 hover:bg-green-900/20",children:[(0,a.jsx)(O.A,{className:"h-4 w-4 mr-1"}),"Approve"]}),(0,a.jsxs)(S.$n,{variant:"outline",size:"sm",onClick:()=>{o(e),h("reject")},className:"border-red-600 text-red-400 hover:text-red-300 hover:bg-red-900/20",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-1"}),"Reject"]})]}),"APPROVED"===e.status&&(0,a.jsxs)(S.$n,{variant:"outline",size:"sm",onClick:()=>{o(e),h("complete")},className:"border-blue-600 text-blue-400 hover:text-blue-300 hover:bg-blue-900/20",children:[(0,a.jsx)(R.A,{className:"h-4 w-4 mr-1"}),"Mark Complete"]})]})]})})},e.id))}),x&&m&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-slate-800 border border-slate-700 rounded-xl max-w-md w-full p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-4 text-white",children:["approve"===m&&"Approve Withdrawal","reject"===m&&"Reject Withdrawal","complete"===m&&"Complete Withdrawal"]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("p",{className:"text-slate-400 mb-2",children:["User: ",(0,a.jsxs)("span",{className:"font-medium text-white",children:[x.user.firstName," ",x.user.lastName]})]}),(0,a.jsxs)("p",{className:"text-slate-400",children:["Amount: ",(0,a.jsx)("span",{className:"font-medium text-white",children:(0,I.vv)(x.amount)})]})]}),"reject"===m&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Rejection Reason *"}),(0,a.jsx)("textarea",{value:j,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent",rows:3,placeholder:"Please provide a reason for rejection...",required:!0})]}),"complete"===m&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Transaction Hash *"}),(0,a.jsx)(S.pd,{value:N,onChange:e=>g(e.target.value),placeholder:"Enter blockchain transaction hash...",className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)(S.$n,{variant:"outline",onClick:()=>{o(null),h(null),b(""),g("")},disabled:v,className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white",children:"Cancel"}),(0,a.jsxs)(S.$n,{onClick:()=>{let e={};"reject"===m&&(e.rejectionReason=j),"complete"===m&&(e.transactionHash=N),A(x.id,m,e)},disabled:v||"reject"===m&&!j.trim()||"complete"===m&&!N.trim(),loading:v,className:"reject"===m?"bg-red-600 hover:bg-red-700 text-white":"approve"===m?"bg-green-600 hover:bg-green-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white",children:["approve"===m&&"Approve","reject"===m&&"Reject","complete"===m&&"Mark Complete"]})]})]})})]})},V=(0,b.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),J=(0,b.A)("percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]),X=()=>{let[e,s]=(0,l.useState)(null),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(!1),[c,d]=(0,l.useState)(!1);(0,l.useEffect)(()=>{x()},[]);let x=async()=>{try{r(!0);let e=await fetch("/api/admin/settings",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("Failed to fetch settings:",e)}finally{r(!1)}},o=async()=>{if(e)try{n(!0),(await fetch("/api/admin/settings",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)})).ok&&(d(!0),setTimeout(()=>d(!1),3e3))}catch(e){console.error("Failed to save settings:",e)}finally{n(!1)}},m=(t,a)=>{e&&s({...e,[t]:a})};return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"System Settings"}),(0,a.jsx)("p",{className:"text-slate-400 mt-1",children:"Configure platform parameters and business rules"})]}),(0,a.jsx)(S.$n,{onClick:o,loading:i,className:"flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(R.A,{className:"h-4 w-4"}),"Saved"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(V,{className:"h-4 w-4"}),"Save Changes"]})})]}),(0,a.jsxs)(S.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(S.aR,{children:(0,a.jsxs)(S.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(P.A,{className:"h-5 w-5 text-blue-400"}),"Mining Unit Pricing"]})}),(0,a.jsx)(S.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"THS Price (USD)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{type:"number",step:"0.01",value:e.thsPriceUSD,onChange:e=>m("thsPriceUSD",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Minimum Purchase"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{type:"number",step:"0.01",value:e.minPurchaseAmount,onChange:e=>m("minPurchaseAmount",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Maximum Purchase"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{type:"number",step:"0.01",value:e.maxPurchaseAmount,onChange:e=>m("maxPurchaseAmount",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"})]})]})]})})]}),(0,a.jsxs)(S.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(S.aR,{children:(0,a.jsxs)(S.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(J,{className:"h-5 w-5 text-orange-400"}),"Earnings Configuration"]})}),(0,a.jsx)(S.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Daily ROI (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(J,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{type:"number",step:"0.01",value:e.dailyROIPercentage,onChange:e=>m("dailyROIPercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Binary Bonus (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(J,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{type:"number",step:"0.01",value:e.binaryBonusPercentage,onChange:e=>m("binaryBonusPercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Referral Bonus (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(J,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{type:"number",step:"0.01",value:e.referralBonusPercentage,onChange:e=>m("referralBonusPercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500"})]})]})]})})]}),(0,a.jsxs)(S.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(S.aR,{children:(0,a.jsxs)(S.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-red-400"}),"Withdrawal Settings"]})}),(0,a.jsx)(S.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Minimum Withdrawal"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{type:"number",step:"0.01",value:e.minWithdrawalAmount,onChange:e=>m("minWithdrawalAmount",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Withdrawal Fee (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(J,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{type:"number",step:"0.01",value:e.withdrawalFeePercentage,onChange:e=>m("withdrawalFeePercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Processing Days"}),(0,a.jsx)(S.pd,{type:"number",value:e.withdrawalProcessingDays,onChange:e=>m("withdrawalProcessingDays",parseInt(e.target.value)||0),className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})]})})]}),(0,a.jsxs)(S.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(S.aR,{children:(0,a.jsxs)(S.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-green-400"}),"Deposit Settings"]})}),(0,a.jsxs)(S.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"USDT TRC20 Deposit Address"}),(0,a.jsx)(S.pd,{type:"text",value:e.usdtDepositAddress,onChange:e=>m("usdtDepositAddress",e.target.value),placeholder:"Enter USDT TRC20 address (e.g., TXXXxxxXXXxxxXXX...)",className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500 font-mono text-sm"}),(0,a.jsx)("p",{className:"text-xs text-slate-400 mt-1",children:"This address will be displayed to users for USDT deposits"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Minimum Deposit"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{type:"number",step:"0.01",value:e.minDepositAmount,onChange:e=>m("minDepositAmount",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Maximum Deposit"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{type:"number",step:"0.01",value:e.maxDepositAmount,onChange:e=>m("maxDepositAmount",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Deposit Fee (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(J,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{type:"number",step:"0.01",value:e.depositFeePercentage,onChange:e=>m("depositFeePercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Minimum Confirmations"}),(0,a.jsx)(S.pd,{type:"number",value:e.minConfirmations,onChange:e=>m("minConfirmations",parseInt(e.target.value)||0),className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"}),(0,a.jsx)("p",{className:"text-xs text-slate-400 mt-1",children:"Number of blockchain confirmations required"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Deposits Enabled"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Allow users to make deposits"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.depositEnabled,onChange:e=>m("depositEnabled",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"})]})]})]})]})]}),(0,a.jsxs)(S.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(S.aR,{children:(0,a.jsxs)(S.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-blue-400"}),"Platform Settings"]})}),(0,a.jsxs)(S.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Platform Fee (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(J,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{type:"number",step:"0.01",value:e.platformFeePercentage,onChange:e=>m("platformFeePercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"})]})]})}),(0,a.jsxs)("div",{className:"space-y-4 pt-4 border-t border-slate-600",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Maintenance Mode"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Temporarily disable platform access"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.maintenanceMode,onChange:e=>m("maintenanceMode",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Registration Enabled"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Allow new user registrations"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.registrationEnabled,onChange:e=>m("registrationEnabled",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"KYC Required"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Require KYC verification for withdrawals"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.kycRequired,onChange:e=>m("kycRequired",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]})]})]})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(D.A,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"Failed to Load Settings"}),(0,a.jsx)("p",{className:"text-slate-400",children:"Unable to load system settings. Please try again."})]})};var Q=t(58559),ee=t(96882);let es=(0,b.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),et=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(""),[c,d]=(0,l.useState)("all"),[x,o]=(0,l.useState)("today"),[m,h]=(0,l.useState)(1),[u,p]=(0,l.useState)(1);(0,l.useEffect)(()=>{j()},[m,i,c,x]);let j=async()=>{try{r(!0);let e=new URLSearchParams({page:m.toString(),limit:"50",search:i,action:c,dateRange:x}),t=await fetch(`/api/admin/logs?${e}`,{credentials:"include"});if(t.ok){let e=await t.json();e.success&&(s(e.data.logs),p(e.data.totalPages))}}catch(e){console.error("Failed to fetch logs:",e)}finally{r(!1)}},b=async()=>{try{let e=new URLSearchParams({search:i,action:c,dateRange:x,export:"true"}),s=await fetch(`/api/admin/logs/export?${e}`,{credentials:"include"});if(s.ok){let e=await s.blob(),t=window.URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download=`system-logs-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a)}}catch(e){console.error("Failed to export logs:",e)}},f=e=>{switch(e){case"USER_LOGIN":case"USER_LOGOUT":return(0,a.jsx)(k.A,{className:"h-4 w-4 text-blue-400"});case"USER_REGISTER":return(0,a.jsx)(R.A,{className:"h-4 w-4 text-green-400"});case"MINING_PURCHASE":case"WITHDRAWAL_REQUEST":return(0,a.jsx)(Q.A,{className:"h-4 w-4 text-purple-400"});case"KYC_SUBMIT":case"KYC_APPROVE":case"KYC_REJECT":return(0,a.jsx)(N,{className:"h-4 w-4 text-orange-400"});case"ADMIN_ACTION":return(0,a.jsx)(D.A,{className:"h-4 w-4 text-red-400"});default:return(0,a.jsx)(ee.A,{className:"h-4 w-4 text-slate-400"})}},g=e=>{switch(e){case"USER_LOGIN":case"USER_REGISTER":case"KYC_APPROVE":return"text-green-300 bg-green-900/20 border border-green-700";case"USER_LOGOUT":return"text-blue-300 bg-blue-900/20 border border-blue-700";case"MINING_PURCHASE":case"WITHDRAWAL_REQUEST":return"text-purple-300 bg-purple-900/20 border border-purple-700";case"KYC_SUBMIT":return"text-orange-300 bg-orange-900/20 border border-orange-700";case"KYC_REJECT":case"ADMIN_ACTION":return"text-red-300 bg-red-900/20 border border-red-700";default:return"text-slate-300 bg-slate-700 border border-slate-600"}},v=e=>"string"==typeof e?e:"object"==typeof e?Object.entries(e).map(([e,s])=>`${e}: ${s}`).join(", "):JSON.stringify(e);return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"System Logs"}),(0,a.jsx)("p",{className:"text-slate-400 mt-1",children:"Monitor platform activity and user actions"})]}),(0,a.jsxs)(S.$n,{onClick:b,variant:"outline",className:"flex items-center gap-2 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white",children:[(0,a.jsx)(es,{className:"h-4 w-4"}),"Export Logs"]})]}),(0,a.jsx)(S.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(S.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(W.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(S.pd,{placeholder:"Search logs...",value:i,onChange:e=>n(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("select",{value:c,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Actions"}),(0,a.jsx)("option",{value:"USER_LOGIN",children:"User Login"}),(0,a.jsx)("option",{value:"USER_REGISTER",children:"User Register"}),(0,a.jsx)("option",{value:"MINING_PURCHASE",children:"Mining Purchase"}),(0,a.jsx)("option",{value:"WITHDRAWAL_REQUEST",children:"Withdrawal Request"}),(0,a.jsx)("option",{value:"KYC_SUBMIT",children:"KYC Submit"}),(0,a.jsx)("option",{value:"ADMIN_ACTION",children:"Admin Action"})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("select",{value:x,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"today",children:"Today"}),(0,a.jsx)("option",{value:"week",children:"This Week"}),(0,a.jsx)("option",{value:"month",children:"This Month"}),(0,a.jsx)("option",{value:"all",children:"All Time"})]})}),(0,a.jsxs)("div",{className:"text-sm text-slate-400 flex items-center",children:[(0,a.jsx)(Q.A,{className:"h-4 w-4 mr-1"}),e.length," logs found"]})]})})}),(0,a.jsxs)(S.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(S.aR,{children:(0,a.jsxs)(S.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(N,{className:"h-5 w-5 text-blue-400"}),"Activity Logs"]})}),(0,a.jsxs)(S.Wu,{children:[(0,a.jsx)("div",{className:"space-y-2",children:0===e.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(N,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No Logs Found"}),(0,a.jsx)("p",{className:"text-slate-400",children:"No activity logs match your current filters."})]}):e.map(e=>(0,a.jsx)("div",{className:"border border-slate-600 rounded-lg p-4 hover:bg-slate-700 transition-colors",children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex items-start gap-3 flex-1",children:[(0,a.jsx)("div",{className:"mt-1",children:f(e.action)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${g(e.action)}`,children:e.action.replace(/_/g," ")}),(0,a.jsx)("span",{className:"text-sm text-slate-400",children:(0,I.Yq)(e.createdAt)})]}),e.user&&(0,a.jsxs)("div",{className:"text-sm text-slate-300 mb-1",children:[(0,a.jsxs)("span",{className:"font-medium",children:[e.user.firstName," ",e.user.lastName]}),(0,a.jsxs)("span",{className:"text-slate-400 ml-2",children:["(",e.user.email,")"]})]}),(0,a.jsx)("div",{className:"text-sm text-slate-400 mb-2",children:v(e.details)}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-xs text-slate-500",children:[(0,a.jsxs)("span",{children:["IP: ",e.ipAddress]}),(0,a.jsxs)("span",{className:"truncate max-w-xs",children:["User Agent: ",e.userAgent]})]})]})]})})},e.id))}),u>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between mt-6 pt-6 border-t border-slate-600",children:[(0,a.jsxs)("div",{className:"text-sm text-slate-400",children:["Page ",m," of ",u]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(S.$n,{variant:"outline",size:"sm",onClick:()=>h(e=>Math.max(1,e-1)),disabled:1===m,className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50",children:"Previous"}),(0,a.jsx)(S.$n,{variant:"outline",size:"sm",onClick:()=>h(e=>Math.min(u,e+1)),disabled:m===u,className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50",children:"Next"})]})]})]})]})]})};function ea(){let{user:e,loading:s}=(0,r.A)();(0,i.useRouter)();let[t,n]=(0,l.useState)("dashboard"),[c,d]=(0,l.useState)(!1),[x,o]=(0,l.useState)(!0);return s||x?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(S.Rh,{size:"lg",text:"Loading admin panel..."})}):e&&c?(0,a.jsx)(C,{activeTab:t,onTabChange:n,children:(()=>{switch(t){case"dashboard":default:return(0,a.jsx)(T,{});case"users":return(0,a.jsx)(_,{});case"kyc":return(0,a.jsx)(q,{});case"deposits":return(0,a.jsx)(K,{});case"withdrawals":return(0,a.jsx)(B,{});case"settings":return(0,a.jsx)(X,{});case"logs":return(0,a.jsx)(et,{})}})()}):null}},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},13964:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28228:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>d});var a=t(65239),l=t(48088),r=t(88170),i=t.n(r),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\admin\\page.tsx"],o={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},61312:(e,s,t)=>{Promise.resolve().then(t.bind(t,13090))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},97760:(e,s,t)=>{Promise.resolve().then(t.bind(t,1132))}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,1771,1332,3866,6549],()=>t(28228));module.exports=a})();