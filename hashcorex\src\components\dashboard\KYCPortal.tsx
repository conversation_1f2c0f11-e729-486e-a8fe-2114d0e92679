'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent, Button, Input } from '@/components/ui';
import { useAuth } from '@/hooks/useAuth';
import { Shield, Upload, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';

interface KYCDocument {
  id: string;
  documentType: 'ID' | 'SELFIE';
  filePath: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  reviewedAt?: string;
  rejectionReason?: string;
  createdAt: string;
}

export const KYCPortal: React.FC = () => {
  const { user, refreshUser } = useAuth();
  const [documents, setDocuments] = useState<KYCDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState('');

  useEffect(() => {
    fetchKYCDocuments();
  }, []);

  const fetchKYCDocuments = async () => {
    try {
      const response = await fetch('/api/kyc/documents', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setDocuments(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch KYC documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (documentType: 'ID' | 'SELFIE', file: File) => {
    setUploading(true);
    setUploadError('');

    try {
      // Validate file
      if (!file.type.startsWith('image/')) {
        throw new Error('Please upload an image file');
      }

      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        throw new Error('File size must be less than 5MB');
      }

      const formData = new FormData();
      formData.append('file', file);
      formData.append('documentType', documentType);

      const response = await fetch('/api/kyc/upload', {
        method: 'POST',
        credentials: 'include',
        body: formData,
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Upload failed');
      }

      // Refresh documents and user data
      await fetchKYCDocuments();
      await refreshUser();

    } catch (err: any) {
      setUploadError(err.message || 'Upload failed');
    } finally {
      setUploading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <CheckCircle className="h-5 w-5 text-eco-500" />;
      case 'REJECTED':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'PENDING':
        return <Clock className="h-5 w-5 text-solar-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-eco-100 text-eco-700';
      case 'REJECTED':
        return 'bg-red-100 text-red-700';
      case 'PENDING':
        return 'bg-solar-100 text-solar-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getDocumentByType = (type: 'ID' | 'SELFIE') => {
    return documents.find(doc => doc.documentType === type);
  };

  const FileUploadSection: React.FC<{ 
    documentType: 'ID' | 'SELFIE'; 
    title: string; 
    description: string;
  }> = ({ documentType, title, description }) => {
    const existingDoc = getDocumentByType(documentType);
    const inputId = `file-${documentType}`;

    return (
      <div className="border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-dark-900">{title}</h3>
            <p className="text-sm text-gray-600">{description}</p>
          </div>
          {existingDoc && (
            <div className="flex items-center space-x-2">
              {getStatusIcon(existingDoc.status)}
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(existingDoc.status)}`}>
                {existingDoc.status}
              </span>
            </div>
          )}
        </div>

        {existingDoc ? (
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <img
                src={existingDoc.filePath}
                alt={`${documentType} document`}
                className="w-16 h-16 object-cover rounded-lg"
              />
              <div className="flex-1">
                <p className="text-sm font-medium text-dark-900">
                  {documentType === 'ID' ? 'ID Document' : 'Selfie Photo'}
                </p>
                <p className="text-xs text-gray-500">
                  Uploaded on {new Date(existingDoc.createdAt).toLocaleDateString()}
                </p>
                {existingDoc.rejectionReason && (
                  <p className="text-xs text-red-600 mt-1">
                    Rejection reason: {existingDoc.rejectionReason}
                  </p>
                )}
              </div>
            </div>

            {existingDoc.status === 'REJECTED' && (
              <div>
                <input
                  id={inputId}
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFileUpload(documentType, file);
                    }
                  }}
                  className="hidden"
                />
                <label
                  htmlFor={inputId}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Re-upload Document
                </label>
              </div>
            )}
          </div>
        ) : (
          <div>
            <input
              id={inputId}
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  handleFileUpload(documentType, file);
                }
              }}
              className="hidden"
            />
            <label
              htmlFor={inputId}
              className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
            >
              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                <Upload className="h-8 w-8 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500">
                  <span className="font-semibold">Click to upload</span> or drag and drop
                </p>
                <p className="text-xs text-gray-500">PNG, JPG up to 5MB</p>
              </div>
            </label>
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-solar-500" />
            <span>KYC Verification</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-32 bg-gray-200 rounded-lg"></div>
            <div className="h-32 bg-gray-200 rounded-lg"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* KYC Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-solar-500" />
            <span>KYC Verification Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            {getStatusIcon(user?.kycStatus || 'PENDING')}
            <div>
              <p className="text-lg font-semibold text-dark-900">
                Status: <span className={`${
                  user?.kycStatus === 'APPROVED' ? 'text-eco-600' :
                  user?.kycStatus === 'REJECTED' ? 'text-red-600' :
                  'text-solar-600'
                }`}>
                  {user?.kycStatus || 'PENDING'}
                </span>
              </p>
              <p className="text-sm text-gray-600">
                {user?.kycStatus === 'APPROVED' && 'Your identity has been verified. You can now make withdrawals.'}
                {user?.kycStatus === 'PENDING' && 'Your documents are being reviewed. This usually takes 1-3 business days.'}
                {user?.kycStatus === 'REJECTED' && 'Your verification was rejected. Please re-upload your documents.'}
              </p>
            </div>
          </div>

          {user?.kycStatus !== 'APPROVED' && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Why do we need KYC verification?</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Comply with international financial regulations</li>
                <li>• Protect your account from unauthorized access</li>
                <li>• Enable secure withdrawals to your wallet</li>
                <li>• Prevent fraud and money laundering</li>
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Document Upload */}
      <Card>
        <CardHeader>
          <CardTitle>Upload Documents</CardTitle>
        </CardHeader>
        <CardContent>
          {uploadError && (
            <div className="mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
              {uploadError}
            </div>
          )}

          {uploading && (
            <div className="mb-4 bg-blue-50 border border-blue-200 text-blue-600 px-4 py-3 rounded-lg text-sm">
              Uploading document... Please wait.
            </div>
          )}

          <div className="space-y-6">
            <FileUploadSection
              documentType="ID"
              title="Government-issued ID"
              description="Upload a clear photo of your passport, driver's license, or national ID card"
            />

            <FileUploadSection
              documentType="SELFIE"
              title="Selfie with ID"
              description="Take a selfie holding your ID document next to your face"
            />
          </div>

          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="text-sm font-medium text-yellow-900 mb-2">Document Requirements:</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• Documents must be clear and readable</li>
              <li>• All four corners of the ID must be visible</li>
              <li>• No blurred, cropped, or edited images</li>
              <li>• Selfie must clearly show your face and the ID</li>
              <li>• File formats: JPG, PNG (max 5MB each)</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
