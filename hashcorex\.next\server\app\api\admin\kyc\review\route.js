"use strict";(()=>{var e={};e.id=2040,e.ids=[2040],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55494:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>p});var a=t(96559),n=t(48088),o=t(37719),i=t(32190),u=t(12909),d=t(6710),c=t(31183);async function p(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return i.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(t.id))return i.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{userId:s,action:a,rejectionReason:n}=await e.json();if(!s||!a||!["APPROVE","REJECT"].includes(a))return i.NextResponse.json({success:!1,error:"Invalid request parameters"},{status:400});if("REJECT"===a&&!n)return i.NextResponse.json({success:!1,error:"Rejection reason is required"},{status:400});let o=await c.z.kYCDocument.findMany({where:{userId:s,status:"PENDING"}});if(0===o.length)return i.NextResponse.json({success:!1,error:"No pending documents found for this user"},{status:404});let p="APPROVE"===a?"APPROVED":"REJECTED",l=new Date;return await c.z.kYCDocument.updateMany({where:{userId:s,status:"PENDING"},data:{status:p,reviewedAt:l,reviewedBy:t.email,rejectionReason:"REJECT"===a?n:null}}),await c.z.user.update({where:{id:s},data:{kycStatus:p}}),await d.AJ.create({action:"KYC_REVIEWED",adminId:t.id,details:{reviewedUserId:s,action:a,rejectionReason:"REJECT"===a?n:null,documentsCount:o.length,reviewedBy:t.email},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),i.NextResponse.json({success:!0,message:`KYC ${a.toLowerCase()}d successfully`,data:{userId:s,status:p,reviewedAt:l,reviewedBy:t.email}})}catch(e){return console.error("KYC review error:",e),i.NextResponse.json({success:!1,error:"Failed to review KYC documents"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/kyc/review/route",pathname:"/api/admin/kyc/review",filename:"route",bundlePath:"app/api/admin/kyc/review/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\review\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:x,serverHooks:m}=l;function v(){return(0,o.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:x})}},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3529],()=>t(55494));module.exports=s})();