"use strict";(()=>{var e={};e.id=8489,e.ids=[8489],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},89199:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>d});var o=t(96559),a=t(48088),u=t(37719),n=t(32190),i=t(12909),p=t(6710);async function d(e){try{let{authenticated:r,user:t}=await (0,i.b9)(e);r&&t&&await p.AJ.create({action:"USER_LOGOUT",userId:t.id,details:{email:t.email,logoutTime:new Date().toISOString()},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"});let s=n.NextResponse.json({success:!0,message:"Logout successful"});return s.cookies.set("auth-token","",{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:0}),s}catch(e){return console.error("Logout error:",e),n.NextResponse.json({success:!1,error:"Logout failed"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/logout/route",pathname:"/api/auth/logout",filename:"route",bundlePath:"app/api/auth/logout/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\logout\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:g}=c;function h(){return(0,u.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3529],()=>t(89199));module.exports=s})();