"use strict";(()=>{var e={};e.id=1767,e.ids=[1767],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65430:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>R,routeModule:()=>w,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{POST:()=>c});var a=t(96559),i=t(48088),n=t(37719),o=t(32190),d=t(12909),u=t(31183),p=t(6710);async function c(e){try{let r,{authenticated:t,user:s}=await (0,d.b9)(e);if(!t||!s)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,d.qc)(s.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let{withdrawalId:a,action:i,rejectionReason:n,transactionHash:c}=await e.json();if(!a||!i)return o.NextResponse.json({error:"Withdrawal ID and action are required"},{status:400});let w=await u.z.withdrawalRequest.findUnique({where:{id:a},include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}});if(!w)return o.NextResponse.json({error:"Withdrawal request not found"},{status:404});let l="",h={withdrawalId:a,userId:w.userId,amount:w.amount,userEmail:w.user.email};switch(i){case"APPROVE":r=await u.z.withdrawalRequest.update({where:{id:a},data:{status:"APPROVED",processedBy:s.id,processedAt:new Date}}),l="WITHDRAWAL_APPROVED";break;case"REJECT":if(!n)return o.NextResponse.json({error:"Rejection reason is required"},{status:400});r=await u.z.withdrawalRequest.update({where:{id:a},data:{status:"REJECTED",processedBy:s.id,processedAt:new Date,rejectionReason:n}}),l="WITHDRAWAL_REJECTED",h.rejectionReason=n;break;case"COMPLETE":if(!c)return o.NextResponse.json({error:"Transaction hash is required"},{status:400});r=await u.z.withdrawalRequest.update({where:{id:a},data:{status:"COMPLETED",processedBy:s.id,processedAt:new Date,txid:c}}),l="WITHDRAWAL_COMPLETED",h.transactionHash=c;break;default:return o.NextResponse.json({error:"Invalid action"},{status:400})}return await p.AJ.create({action:l,userId:s.id,details:h,ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:`Withdrawal ${i.toLowerCase()}d successfully`,data:r})}catch(e){return console.error("Admin withdrawal action error:",e),o.NextResponse.json({success:!1,error:"Failed to perform withdrawal action"},{status:500})}}let w=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/withdrawals/action/route",pathname:"/api/admin/withdrawals/action",filename:"route",bundlePath:"app/api/admin/withdrawals/action/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\withdrawals\\action\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:h,serverHooks:x}=w;function R(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:h})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3529],()=>t(65430));module.exports=s})();