'use client';

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl',
  {
    variants: {
      variant: {
        primary: 'bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500',
        secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500',
        success: 'bg-emerald-500 text-white hover:bg-emerald-600 focus:ring-emerald-500',
        danger: 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500',
        outline: 'border-2 border-yellow-500 bg-transparent text-yellow-600 hover:bg-yellow-500 hover:text-white focus:ring-yellow-500',
        ghost: 'text-gray-600 hover:bg-yellow-50 hover:text-yellow-700 focus:ring-yellow-500 rounded-lg',
        link: 'text-yellow-600 underline-offset-4 hover:underline focus:ring-yellow-500 hover:text-yellow-700',
        premium: 'bg-slate-800 text-white hover:bg-slate-900 focus:ring-slate-500',
        glass: 'glass-morphism text-slate-900 hover:bg-white/20 backdrop-blur-xl border border-white/20',
      },
      size: {
        sm: 'h-10 px-4 text-sm rounded-lg',
        md: 'h-12 px-6 text-base rounded-xl',
        lg: 'h-14 px-8 text-lg rounded-xl',
        xl: 'h-16 px-10 text-xl rounded-2xl font-bold',
        icon: 'h-12 w-12 rounded-xl',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, loading, leftIcon, rightIcon, children, disabled, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <div className="mr-2">
            <div className="spinner" />
          </div>
        )}
        {leftIcon && !loading && <span className="mr-2">{leftIcon}</span>}
        {children}
        {rightIcon && !loading && <span className="ml-2">{rightIcon}</span>}
      </button>
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };
