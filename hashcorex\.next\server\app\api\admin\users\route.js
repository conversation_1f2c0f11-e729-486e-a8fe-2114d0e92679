"use strict";(()=>{var e={};e.id=6950,e.ids=[6950],e.modules={1304:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>c});var a=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(12909),p=t(31183);async function c(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,u.qc)(t.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let{searchParams:s}=new URL(e.url),a=parseInt(s.get("page")||"1"),i=parseInt(s.get("limit")||"20"),n=s.get("search")||"",c=s.get("status")||"all",d={};n&&(d.OR=[{email:{contains:n,mode:"insensitive"}},{firstName:{contains:n,mode:"insensitive"}},{lastName:{contains:n,mode:"insensitive"}},{referralId:{contains:n,mode:"insensitive"}}]),"all"!==c&&(d.isActive="active"===c);let[l,m]=await Promise.all([p.z.user.findMany({where:d,select:{id:!0,email:!0,firstName:!0,lastName:!0,referralId:!0,role:!0,isActive:!0,kycStatus:!0,createdAt:!0,_count:{select:{miningUnits:!0,transactions:!0}}},orderBy:{createdAt:"desc"},skip:(a-1)*i,take:i}),p.z.user.count({where:d})]),x=Math.ceil(m/i);return o.NextResponse.json({success:!0,data:{users:l,pagination:{currentPage:a,totalPages:x,totalCount:m,hasNext:a<x,hasPrev:a>1}}})}catch(e){return console.error("Admin users fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch users"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/users/route",pathname:"/api/admin/users",filename:"route",bundlePath:"app/api/admin/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\users\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:x}=d;function v(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3529],()=>t(1304));module.exports=s})();