/**
 * Trongrid API Integration for USDT TRC20 Transaction Verification
 * 
 * This module provides utilities to interact with the Trongrid API
 * to verify USDT TRC20 transactions on the Tron blockchain.
 */

// Trongrid API endpoints
const TRONGRID_API_BASE = 'https://api.trongrid.io';
const TRONGRID_API_KEY = process.env.TRONGRID_API_KEY; // Optional, for higher rate limits

// USDT TRC20 contract address on Tron
const USDT_TRC20_CONTRACT = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';

// Rate limiting configuration
const RATE_LIMIT_DELAY = 1000; // 1 second between requests
let lastRequestTime = 0;

interface TronTransaction {
  txID: string;
  blockNumber: number;
  blockTimeStamp: number;
  contractResult: string[];
  receipt: {
    result: string;
  };
  log: Array<{
    address: string;
    topics: string[];
    data: string;
  }>;
}

interface TronTransactionInfo {
  id: string;
  fee: number;
  blockNumber: number;
  blockTimeStamp: number;
  contractResult: string[];
  receipt: {
    result: string;
  };
  log: Array<{
    address: string;
    topics: string[];
    data: string;
  }>;
}

interface TronAccountInfo {
  address: string;
  balance: number;
  create_time: number;
  latest_opration_time: number;
}

interface USDTTransferDetails {
  isValid: boolean;
  amount: number;
  fromAddress: string;
  toAddress: string;
  contractAddress: string;
  blockNumber: number;
  blockTimestamp: number;
  confirmations: number;
  transactionId: string;
}

/**
 * Rate limiting helper to prevent API abuse
 */
async function rateLimitDelay(): Promise<void> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
    const delay = RATE_LIMIT_DELAY - timeSinceLastRequest;
    await new Promise(resolve => setTimeout(resolve, delay));
  }
  
  lastRequestTime = Date.now();
}

/**
 * Make HTTP request to Trongrid API with proper headers and error handling
 */
async function makeApiRequest(endpoint: string): Promise<any> {
  await rateLimitDelay();
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  if (TRONGRID_API_KEY) {
    headers['TRON-PRO-API-KEY'] = TRONGRID_API_KEY;
  }
  
  const response = await fetch(`${TRONGRID_API_BASE}${endpoint}`, {
    method: 'GET',
    headers,
  });
  
  if (!response.ok) {
    throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);
  }
  
  return await response.json();
}

/**
 * Get transaction details by transaction ID
 */
export async function getTransactionById(txId: string): Promise<TronTransaction | null> {
  try {
    const data = await makeApiRequest(`/v1/transactions/${txId}`);
    return data.data && data.data.length > 0 ? data.data[0] : null;
  } catch (error) {
    console.error('Error fetching transaction:', error);
    return null;
  }
}

/**
 * Get transaction info (including receipt) by transaction ID
 */
export async function getTransactionInfo(txId: string): Promise<TronTransactionInfo | null> {
  try {
    const data = await makeApiRequest(`/wallet/gettransactioninfobyid?value=${txId}`);
    return data.id ? data : null;
  } catch (error) {
    console.error('Error fetching transaction info:', error);
    return null;
  }
}

/**
 * Convert hex string to decimal number
 */
function hexToDecimal(hex: string): number {
  return parseInt(hex, 16);
}

/**
 * Convert Tron address from hex to base58
 */
function hexToTronAddress(hex: string): string {
  // Remove '0x' prefix if present
  const cleanHex = hex.startsWith('0x') ? hex.slice(2) : hex;
  
  // Add '41' prefix for Tron mainnet addresses
  const fullHex = '41' + cleanHex;
  
  // This is a simplified conversion - in production, use a proper Tron address library
  // For now, we'll return the hex format as it's used internally
  return 'T' + cleanHex.substring(0, 33); // Simplified conversion
}

/**
 * Parse USDT TRC20 transfer from transaction logs
 */
function parseUSDTTransfer(logs: Array<{ address: string; topics: string[]; data: string }>): {
  amount: number;
  fromAddress: string;
  toAddress: string;
} | null {
  // Find the USDT transfer log
  const usdtLog = logs.find(log => 
    log.address.toLowerCase() === USDT_TRC20_CONTRACT.toLowerCase() &&
    log.topics.length >= 3 &&
    log.topics[0] === '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef' // Transfer event signature
  );
  
  if (!usdtLog) {
    return null;
  }
  
  try {
    // Parse transfer details from log
    const fromAddress = hexToTronAddress(usdtLog.topics[1].slice(26)); // Remove padding
    const toAddress = hexToTronAddress(usdtLog.topics[2].slice(26)); // Remove padding
    const amount = hexToDecimal(usdtLog.data) / 1000000; // USDT has 6 decimals
    
    return {
      amount,
      fromAddress,
      toAddress,
    };
  } catch (error) {
    console.error('Error parsing USDT transfer:', error);
    return null;
  }
}

/**
 * Verify USDT TRC20 transaction and extract transfer details
 */
export async function verifyUSDTTransaction(
  txId: string,
  expectedToAddress: string,
  minConfirmations: number = 1
): Promise<USDTTransferDetails> {
  try {
    // Get transaction details
    const transaction = await getTransactionById(txId);
    if (!transaction) {
      return {
        isValid: false,
        amount: 0,
        fromAddress: '',
        toAddress: '',
        contractAddress: '',
        blockNumber: 0,
        blockTimestamp: 0,
        confirmations: 0,
        transactionId: txId,
      };
    }
    
    // Get transaction info for receipt and confirmations
    const transactionInfo = await getTransactionInfo(txId);
    if (!transactionInfo) {
      return {
        isValid: false,
        amount: 0,
        fromAddress: '',
        toAddress: '',
        contractAddress: '',
        blockNumber: 0,
        blockTimestamp: 0,
        confirmations: 0,
        transactionId: txId,
      };
    }
    
    // Check if transaction was successful
    if (transactionInfo.receipt.result !== 'SUCCESS') {
      return {
        isValid: false,
        amount: 0,
        fromAddress: '',
        toAddress: '',
        contractAddress: USDT_TRC20_CONTRACT,
        blockNumber: transactionInfo.blockNumber,
        blockTimestamp: transactionInfo.blockTimeStamp,
        confirmations: 0,
        transactionId: txId,
      };
    }
    
    // Parse USDT transfer from logs
    const transferDetails = parseUSDTTransfer(transactionInfo.log || []);
    if (!transferDetails) {
      return {
        isValid: false,
        amount: 0,
        fromAddress: '',
        toAddress: '',
        contractAddress: USDT_TRC20_CONTRACT,
        blockNumber: transactionInfo.blockNumber,
        blockTimestamp: transactionInfo.blockTimeStamp,
        confirmations: 0,
        transactionId: txId,
      };
    }
    
    // Calculate confirmations (simplified - in production, get current block height)
    const currentTime = Date.now();
    const transactionTime = transactionInfo.blockTimeStamp;
    const confirmations = Math.floor((currentTime - transactionTime) / 3000); // ~3 seconds per block
    
    // Verify the recipient address matches expected address
    const isValidRecipient = transferDetails.toAddress.toLowerCase() === expectedToAddress.toLowerCase();
    
    return {
      isValid: isValidRecipient && confirmations >= minConfirmations && transferDetails.amount > 0,
      amount: transferDetails.amount,
      fromAddress: transferDetails.fromAddress,
      toAddress: transferDetails.toAddress,
      contractAddress: USDT_TRC20_CONTRACT,
      blockNumber: transactionInfo.blockNumber,
      blockTimestamp: transactionInfo.blockTimeStamp,
      confirmations: Math.max(0, confirmations),
      transactionId: txId,
    };
    
  } catch (error) {
    console.error('Error verifying USDT transaction:', error);
    return {
      isValid: false,
      amount: 0,
      fromAddress: '',
      toAddress: '',
      contractAddress: '',
      blockNumber: 0,
      blockTimestamp: 0,
      confirmations: 0,
      transactionId: txId,
    };
  }
}

/**
 * Validate Tron transaction ID format
 */
export function isValidTronTransactionId(txId: string): boolean {
  // Tron transaction IDs are 64-character hexadecimal strings
  const tronTxRegex = /^[a-fA-F0-9]{64}$/;
  return tronTxRegex.test(txId);
}

/**
 * Validate Tron address format
 */
export function isValidTronAddress(address: string): boolean {
  // Tron addresses start with 'T' and are 34 characters long
  const tronAddressRegex = /^T[A-Za-z1-9]{33}$/;
  return tronAddressRegex.test(address);
}

/**
 * Get account information by address
 */
export async function getAccountInfo(address: string): Promise<TronAccountInfo | null> {
  try {
    const data = await makeApiRequest(`/v1/accounts/${address}`);
    return data.data && data.data.length > 0 ? data.data[0] : null;
  } catch (error) {
    console.error('Error fetching account info:', error);
    return null;
  }
}
