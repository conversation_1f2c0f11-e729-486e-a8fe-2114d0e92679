"use strict";(()=>{var e={};e.id=1612,e.ids=[1612],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")},97133:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{POST:()=>d});var a=s(96559),o=s(48088),n=s(37719),i=s(32190),u=s(12909),p=s(6710);async function d(e){try{let{email:r,firstName:s,lastName:t,password:a,confirmPassword:o,referralCode:n}=await e.json();if(!r||!s||!t||!a||!o)return i.NextResponse.json({success:!1,error:"All fields are required"},{status:400});if(!(0,u.DT)(r))return i.NextResponse.json({success:!1,error:"Invalid email format"},{status:400});if(a!==o)return i.NextResponse.json({success:!1,error:"Passwords do not match"},{status:400});let d=(0,u.Oj)(a);if(!d.valid)return i.NextResponse.json({success:!1,error:d.errors.join(", ")},{status:400});let c=new URL(e.url).searchParams.get("side"),l=await (0,u.DY)({email:r,firstName:s,lastName:t,password:a,referralCode:n,placementSide:c||void 0});return await p.AJ.create({action:"USER_REGISTERED",userId:l.id,details:{email:l.email,referralCode:n||null},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),i.NextResponse.json({success:!0,message:"Registration successful",data:l})}catch(e){return console.error("Registration error:",e),i.NextResponse.json({success:!1,error:e.message||"Registration failed"},{status:400})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:g}=c;function m(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,580,5315,3529],()=>s(97133));module.exports=t})();