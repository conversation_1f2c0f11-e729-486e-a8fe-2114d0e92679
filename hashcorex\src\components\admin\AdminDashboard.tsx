'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui';
import { Grid } from '@/components/layout';
import { 
  Users, 
  DollarSign, 
  TrendingUp, 
  Zap, 
  Shield, 
  CreditCard,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { formatCurrency, formatNumber, formatTHS } from '@/lib/utils';

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  pendingKYC: number;
  approvedKYC: number;
  totalInvestments: number;
  totalEarningsDistributed: number;
  totalTHSSold: number;
  activeTHS: number;
  pendingWithdrawals: number;
  totalWithdrawals: number;
  platformRevenue: number;
  binaryPoolBalance: number;
}

export const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAdminStats();
  }, []);

  const fetchAdminStats = async () => {
    try {
      const response = await fetch('/api/admin/stats', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStats(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch admin stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-32 bg-slate-700 rounded-xl"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!stats) {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="text-center py-8">
          <p className="text-slate-400">Failed to load admin statistics</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-slate-800 border border-slate-700 rounded-xl p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">Admin Dashboard</h1>
        <p className="text-slate-300">
          Monitor platform performance, manage users, and oversee all operations.
        </p>
      </div>

      {/* User Statistics */}
      <div>
        <h2 className="text-lg font-semibold text-white mb-4">User Management</h2>
        <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={6}>
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Total Users</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatNumber(stats.totalUsers, 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Active Users</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatNumber(stats.activeUsers, 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Pending KYC</p>
                  <p className="text-2xl font-bold text-orange-400">
                    {formatNumber(stats.pendingKYC, 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center">
                  <Shield className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Approved KYC</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatNumber(stats.approvedKYC, 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <Shield className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Financial Statistics */}
      <div>
        <h2 className="text-lg font-semibold text-white mb-4">Financial Overview</h2>
        <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={6}>
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Total Investments</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatCurrency(stats.totalInvestments)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Earnings Distributed</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatCurrency(stats.totalEarningsDistributed)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Platform Revenue</p>
                  <p className="text-2xl font-bold text-orange-400">
                    {formatCurrency(stats.platformRevenue)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Binary Pool</p>
                  <p className="text-2xl font-bold text-red-400">
                    {formatCurrency(stats.binaryPoolBalance)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-red-600 rounded-full flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Mining Statistics */}
      <div>
        <h2 className="text-lg font-semibold text-white mb-4">Mining Operations</h2>
        <Grid cols={{ default: 1, md: 2 }} gap={6}>
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Total TH/s Sold</p>
                  <p className="text-2xl font-bold text-orange-400">
                    {formatTHS(stats.totalTHSSold)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center">
                  <Zap className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Active TH/s</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatTHS(stats.activeTHS)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <Zap className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Withdrawal Management */}
      <div>
        <h2 className="text-lg font-semibold text-white mb-4">Withdrawal Management</h2>
        <Grid cols={{ default: 1, md: 2 }} gap={6}>
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Pending Withdrawals</p>
                  <p className="text-2xl font-bold text-red-400">
                    {formatNumber(stats.pendingWithdrawals, 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-red-600 rounded-full flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Total Withdrawals</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatCurrency(stats.totalWithdrawals)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <CreditCard className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Quick Actions */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <Grid cols={{ default: 1, md: 3 }} gap={4}>
            <div className="p-4 border border-slate-600 rounded-lg hover:bg-slate-700 cursor-pointer transition-colors">
              <div className="flex items-center space-x-3">
                <Shield className="h-8 w-8 text-orange-400" />
                <div>
                  <h3 className="font-medium text-white">Review KYC</h3>
                  <p className="text-sm text-slate-400">{stats.pendingKYC} pending reviews</p>
                </div>
              </div>
            </div>

            <div className="p-4 border border-slate-600 rounded-lg hover:bg-slate-700 cursor-pointer transition-colors">
              <div className="flex items-center space-x-3">
                <CreditCard className="h-8 w-8 text-red-400" />
                <div>
                  <h3 className="font-medium text-white">Process Withdrawals</h3>
                  <p className="text-sm text-slate-400">{stats.pendingWithdrawals} pending</p>
                </div>
              </div>
            </div>

            <div className="p-4 border border-slate-600 rounded-lg hover:bg-slate-700 cursor-pointer transition-colors">
              <div className="flex items-center space-x-3">
                <Users className="h-8 w-8 text-blue-400" />
                <div>
                  <h3 className="font-medium text-white">Manage Users</h3>
                  <p className="text-sm text-slate-400">{stats.totalUsers} total users</p>
                </div>
              </div>
            </div>
          </Grid>
        </CardContent>
      </Card>
    </div>
  );
};
