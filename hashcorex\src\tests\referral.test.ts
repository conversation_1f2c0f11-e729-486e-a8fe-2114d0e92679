import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { 
  placeUserInBinaryTree, 
  placeUserInSpecificSide,
  calculateDownlineCount,
  getDetailedTeamStats,
  getTreeHealthStats,
  searchUsersInTree,
  getBinaryTreeStructure
} from '../lib/referral';
import { validateReferralCode, validatePlacementRequest } from '../lib/referralValidation';
import { prisma } from '../lib/prisma';

// Mock data for testing
const mockUsers = [
  {
    id: 'user1',
    email: '<EMAIL>',
    firstName: 'User',
    lastName: 'One',
    referralId: 'REF001',
    isActive: true,
  },
  {
    id: 'user2',
    email: '<EMAIL>',
    firstName: 'User',
    lastName: 'Two',
    referralId: 'REF002',
    isActive: true,
  },
  {
    id: 'user3',
    email: '<EMAIL>',
    firstName: 'User',
    lastName: 'Three',
    referralId: 'REF003',
    isActive: true,
  },
];

describe('Binary Tree Placement Algorithm', () => {
  beforeEach(async () => {
    // Clean up test data
    await prisma.referral.deleteMany({});
    await prisma.binaryPoints.deleteMany({});
    await prisma.user.deleteMany({});
    
    // Create test users
    for (const user of mockUsers) {
      await prisma.user.create({
        data: {
          ...user,
          password: 'hashedpassword',
        },
      });
    }
  });

  afterEach(async () => {
    // Clean up after tests
    await prisma.referral.deleteMany({});
    await prisma.binaryPoints.deleteMany({});
    await prisma.user.deleteMany({});
  });

  describe('placeUserInBinaryTree', () => {
    it('should place first user in left side', async () => {
      const placementSide = await placeUserInBinaryTree('user1', 'user2');
      expect(placementSide).toBe('LEFT');

      const referral = await prisma.referral.findFirst({
        where: { referrerId: 'user1', referredId: 'user2' },
      });
      expect(referral?.placementSide).toBe('LEFT');
    });

    it('should place second user in right side for balance', async () => {
      // Place first user
      await placeUserInBinaryTree('user1', 'user2');
      
      // Place second user
      const placementSide = await placeUserInBinaryTree('user1', 'user3');
      expect(placementSide).toBe('RIGHT');

      const referral = await prisma.referral.findFirst({
        where: { referrerId: 'user1', referredId: 'user3' },
      });
      expect(referral?.placementSide).toBe('RIGHT');
    });

    it('should use weaker leg logic for optimal placement', async () => {
      // Create a tree structure where left side has more users
      await placeUserInBinaryTree('user1', 'user2'); // Left
      await placeUserInBinaryTree('user1', 'user3'); // Right
      
      // Add more users to left side to make it heavier
      const user4 = await prisma.user.create({
        data: {
          id: 'user4',
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'Four',
          referralId: 'REF004',
          password: 'hashedpassword',
          isActive: true,
        },
      });
      
      await placeUserInBinaryTree('user2', 'user4'); // Under left side
      
      // Now place a new user - should go to right side (weaker leg)
      const user5 = await prisma.user.create({
        data: {
          id: 'user5',
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'Five',
          referralId: 'REF005',
          password: 'hashedpassword',
          isActive: true,
        },
      });
      
      const placementSide = await placeUserInBinaryTree('user1', 'user5');
      
      // Should place in right side or under right side (weaker leg)
      const referral = await prisma.referral.findFirst({
        where: { referredId: 'user5' },
      });
      
      expect(referral).toBeTruthy();
      // The exact placement depends on the algorithm, but it should balance the tree
    });
  });

  describe('placeUserInSpecificSide', () => {
    it('should place user in requested side when available', async () => {
      const placementSide = await placeUserInSpecificSide('user1', 'user2', 'RIGHT');
      expect(placementSide).toBe('RIGHT');

      const referral = await prisma.referral.findFirst({
        where: { referrerId: 'user1', referredId: 'user2' },
      });
      expect(referral?.placementSide).toBe('RIGHT');
    });

    it('should find next available spot in requested side when direct spot is occupied', async () => {
      // Fill direct right spot
      await placeUserInSpecificSide('user1', 'user2', 'RIGHT');
      
      // Try to place another user in right side
      const placementSide = await placeUserInSpecificSide('user1', 'user3', 'RIGHT');
      
      // Should still place in right side (under user2)
      const referral = await prisma.referral.findFirst({
        where: { referredId: 'user3' },
      });
      
      expect(referral).toBeTruthy();
      expect(placementSide).toBe('RIGHT');
    });
  });

  describe('calculateDownlineCount', () => {
    it('should return 0 for user with no downline', async () => {
      const leftCount = await calculateDownlineCount('user1', 'LEFT');
      const rightCount = await calculateDownlineCount('user1', 'RIGHT');
      
      expect(leftCount).toBe(0);
      expect(rightCount).toBe(0);
    });

    it('should correctly count downline users', async () => {
      // Create a tree structure
      await placeUserInBinaryTree('user1', 'user2'); // Left
      await placeUserInBinaryTree('user1', 'user3'); // Right
      
      const leftCount = await calculateDownlineCount('user1', 'LEFT');
      const rightCount = await calculateDownlineCount('user1', 'RIGHT');
      
      expect(leftCount).toBe(1);
      expect(rightCount).toBe(1);
    });
  });
});

describe('Tree Statistics and Health', () => {
  beforeEach(async () => {
    await prisma.referral.deleteMany({});
    await prisma.binaryPoints.deleteMany({});
    await prisma.user.deleteMany({});
    
    for (const user of mockUsers) {
      await prisma.user.create({
        data: {
          ...user,
          password: 'hashedpassword',
        },
      });
    }
  });

  describe('getDetailedTeamStats', () => {
    it('should return correct team statistics', async () => {
      // Create some referrals
      await placeUserInBinaryTree('user1', 'user2');
      await placeUserInBinaryTree('user1', 'user3');
      
      const stats = await getDetailedTeamStats('user1');
      
      expect(stats.totalTeam).toBeGreaterThanOrEqual(2);
      expect(stats.leftTeam).toBeGreaterThanOrEqual(0);
      expect(stats.rightTeam).toBeGreaterThanOrEqual(0);
      expect(stats.directReferrals).toBeGreaterThanOrEqual(0);
    });
  });

  describe('getTreeHealthStats', () => {
    it('should calculate tree balance ratio correctly', async () => {
      // Create balanced tree
      await placeUserInBinaryTree('user1', 'user2'); // Left
      await placeUserInBinaryTree('user1', 'user3'); // Right
      
      const health = await getTreeHealthStats('user1');
      
      expect(health.balanceRatio).toBeGreaterThan(0);
      expect(health.balanceRatio).toBeLessThanOrEqual(1);
      expect(health.totalUsers).toBeGreaterThanOrEqual(2);
    });
  });
});

describe('Referral Validation', () => {
  beforeEach(async () => {
    await prisma.user.deleteMany({});
    
    await prisma.user.create({
      data: {
        id: 'user1',
        email: '<EMAIL>',
        firstName: 'User',
        lastName: 'One',
        referralId: 'VALID001',
        password: 'hashedpassword',
        isActive: true,
      },
    });

    await prisma.user.create({
      data: {
        id: 'user2',
        email: '<EMAIL>',
        firstName: 'User',
        lastName: 'Two',
        referralId: 'INACTIVE001',
        password: 'hashedpassword',
        isActive: false,
      },
    });
  });

  describe('validateReferralCode', () => {
    it('should validate correct referral code', async () => {
      const result = await validateReferralCode('VALID001');
      
      expect(result.isValid).toBe(true);
      expect(result.referrer).toBeTruthy();
      expect(result.referrer?.id).toBe('user1');
    });

    it('should reject invalid referral code', async () => {
      const result = await validateReferralCode('INVALID001');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid referral code');
    });

    it('should reject inactive referrer', async () => {
      const result = await validateReferralCode('INACTIVE001');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Referrer account is inactive');
    });

    it('should reject empty referral code', async () => {
      const result = await validateReferralCode('');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Referral code is required');
    });
  });

  describe('validatePlacementRequest', () => {
    it('should validate placement request for active referrer', async () => {
      const result = await validatePlacementRequest('user1');
      
      expect(result.isValid).toBe(true);
      expect(result.canPlace).toBe(true);
    });

    it('should reject placement request for inactive referrer', async () => {
      const result = await validatePlacementRequest('user2');
      
      expect(result.isValid).toBe(false);
      expect(result.canPlace).toBe(false);
      expect(result.error).toBe('Referrer account is inactive');
    });

    it('should reject placement request for non-existent referrer', async () => {
      const result = await validatePlacementRequest('nonexistent');
      
      expect(result.isValid).toBe(false);
      expect(result.canPlace).toBe(false);
      expect(result.error).toBe('Referrer not found');
    });
  });
});

describe('Tree Search Functionality', () => {
  beforeEach(async () => {
    await prisma.referral.deleteMany({});
    await prisma.user.deleteMany({});
    
    // Create test users with searchable data
    await prisma.user.create({
      data: {
        id: 'user1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        referralId: 'REF001',
        password: 'hashedpassword',
        isActive: true,
      },
    });

    await prisma.user.create({
      data: {
        id: 'user2',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        referralId: 'REF002',
        password: 'hashedpassword',
        isActive: true,
      },
    });

    // Create tree structure
    await placeUserInBinaryTree('user1', 'user2');
  });

  describe('searchUsersInTree', () => {
    it('should find users by first name', async () => {
      const results = await searchUsersInTree('user1', 'Jane');
      
      expect(results.length).toBeGreaterThan(0);
      expect(results[0].firstName).toBe('Jane');
    });

    it('should find users by email', async () => {
      const results = await searchUsersInTree('user1', 'jane.smith');
      
      expect(results.length).toBeGreaterThan(0);
      expect(results[0].email).toBe('<EMAIL>');
    });

    it('should return empty array for non-matching search', async () => {
      const results = await searchUsersInTree('user1', 'nonexistent');
      
      expect(results.length).toBe(0);
    });
  });
});
