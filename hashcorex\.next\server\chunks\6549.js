"use strict";exports.id=6549,exports.ids=[6549],exports.modules={5336:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},12941:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},16189:(e,t,a)=>{var l=a(65773);a.o(l,"useRouter")&&a.d(t,{useRouter:function(){return l.useRouter}}),a.o(l,"useSearchParams")&&a.d(t,{useSearchParams:function(){return l.useSearchParams}})},23928:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25541:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},35071:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},36679:(e,t,a)=>{a.d(t,{mc:()=>r,so:()=>i,xA:()=>c,si:()=>x});var l=a(60687);a(43210);var s=a(4780);let r=({children:e,className:t,size:a="lg"})=>(0,l.jsx)("div",{className:(0,s.cn)("mx-auto px-4 sm:px-6 lg:px-8",{sm:"max-w-2xl",md:"max-w-4xl",lg:"max-w-6xl",xl:"max-w-7xl",full:"max-w-full"}[a],t),children:e}),c=({children:e,className:t,cols:a={default:1,md:2,lg:3},gap:r=6})=>(0,l.jsx)("div",{className:(0,s.cn)((()=>{let e=["grid"];return a.default&&e.push(`grid-cols-${a.default}`),a.sm&&e.push(`sm:grid-cols-${a.sm}`),a.md&&e.push(`md:grid-cols-${a.md}`),a.lg&&e.push(`lg:grid-cols-${a.lg}`),a.xl&&e.push(`xl:grid-cols-${a.xl}`),e.push(`gap-${r}`),e.join(" ")})(),t),children:e}),i=({children:e,className:t,direction:a="row",align:r="start",justify:c="start",wrap:i="nowrap",gap:h=0})=>(0,l.jsx)("div",{className:(0,s.cn)("flex",{row:"flex-row",col:"flex-col","row-reverse":"flex-row-reverse","col-reverse":"flex-col-reverse"}[a],{start:"items-start",center:"items-center",end:"items-end",stretch:"items-stretch",baseline:"items-baseline"}[r],{start:"justify-start",center:"justify-center",end:"justify-end",between:"justify-between",around:"justify-around",evenly:"justify-evenly"}[c],{wrap:"flex-wrap",nowrap:"flex-nowrap","wrap-reverse":"flex-wrap-reverse"}[i],h>0&&`gap-${h}`,t),children:e});var h=a(85814),d=a.n(h),n=a(61212),o=a(71180);let x=({children:e})=>(0,l.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,l.jsx)("nav",{className:"fixed top-0 w-full z-50 glass-morphism border-b border-white/20",children:(0,l.jsx)(r,{children:(0,l.jsxs)(i,{justify:"between",align:"center",className:"h-20",children:[(0,l.jsxs)(d(),{href:"/",className:"flex items-center space-x-3",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(o.MX,{className:"h-10 w-10 text-yellow-500 animate-pulse"}),(0,l.jsx)("div",{className:"absolute inset-0 bg-yellow-500/20 rounded-full animate-ping"})]}),(0,l.jsx)("span",{className:"text-3xl font-black text-slate-900",children:"HashCoreX"})]}),(0,l.jsxs)(i,{align:"center",gap:8,children:[(0,l.jsx)(d(),{href:"/about",className:"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105",children:"About"}),(0,l.jsx)(d(),{href:"/how-it-works",className:"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105",children:"How It Works"}),(0,l.jsx)(d(),{href:"/login",children:(0,l.jsx)(n.$n,{variant:"ghost",size:"md",className:"font-semibold",children:"Login"})}),(0,l.jsx)(d(),{href:"/register",children:(0,l.jsx)(n.$n,{variant:"primary",size:"md",className:"font-semibold",children:"Get Started"})})]})]})})}),(0,l.jsx)("main",{className:"pt-20",children:e}),(0,l.jsx)("footer",{className:"bg-dark-900 text-white py-12",children:(0,l.jsxs)(r,{children:[(0,l.jsxs)(c,{cols:{default:1,md:4},gap:8,children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)(i,{align:"center",gap:2,className:"mb-4",children:[(0,l.jsx)(o.MX,{className:"h-8 w-8 text-yellow-400"}),(0,l.jsx)("span",{className:"text-2xl font-bold",children:"HashCoreX"})]}),(0,l.jsx)("p",{className:"text-gray-400",children:"Sustainable cryptocurrency mining powered by renewable energy."})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-4",children:"Platform"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(d(),{href:"/about",className:"block text-gray-400 hover:text-white transition-colors",children:"About Us"}),(0,l.jsx)(d(),{href:"/how-it-works",className:"block text-gray-400 hover:text-white transition-colors",children:"How It Works"}),(0,l.jsx)(d(),{href:"/pricing",className:"block text-gray-400 hover:text-white transition-colors",children:"Pricing"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-4",children:"Support"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(d(),{href:"/contact",className:"block text-gray-400 hover:text-white transition-colors",children:"Contact Us"}),(0,l.jsx)(d(),{href:"/faq",className:"block text-gray-400 hover:text-white transition-colors",children:"FAQ"}),(0,l.jsx)(d(),{href:"/help",className:"block text-gray-400 hover:text-white transition-colors",children:"Help Center"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-4",children:"Legal"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(d(),{href:"/privacy",className:"block text-gray-400 hover:text-white transition-colors",children:"Privacy Policy"}),(0,l.jsx)(d(),{href:"/terms",className:"block text-gray-400 hover:text-white transition-colors",children:"Terms of Service"}),(0,l.jsx)(d(),{href:"/compliance",className:"block text-gray-400 hover:text-white transition-colors",children:"Compliance"})]})]})]}),(0,l.jsx)("div",{className:"border-t border-gray-800 mt-12 pt-8 text-center text-gray-400",children:(0,l.jsx)("p",{children:"\xa9 2024 HashCoreX. All rights reserved."})})]})})]})},40083:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},40228:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},43649:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},45583:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},48730:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49625:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},58559:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},58869:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},78122:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78272:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},84027:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},85778:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},96882:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},97051:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},99270:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},99891:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};