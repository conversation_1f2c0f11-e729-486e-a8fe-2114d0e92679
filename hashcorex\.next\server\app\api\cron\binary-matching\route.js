(()=>{var e={};e.id=1614,e.ids=[1614],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,a)=>{"use strict";a.d(t,{AJ:()=>l,DR:()=>n,FW:()=>c,Gy:()=>r,J6:()=>w,cc:()=>d,k_:()=>p,rs:()=>u,tg:()=>i,wJ:()=>o});var s=a(31183);let r={create:async e=>await s.z.user.create({data:{email:e.email,firstName:e.firstName,lastName:e.lastName,password:e.password,referralId:e.referralId||void 0}}),findByEmail:async e=>await s.z.user.findUnique({where:{email:e},include:{miningUnits:!0,transactions:!0,binaryPoints:!0}}),findById:async e=>await s.z.user.findUnique({where:{id:e},include:{miningUnits:!0,transactions:!0,binaryPoints:!0}}),findByReferralId:async e=>await s.z.user.findUnique({where:{referralId:e}}),update:async(e,t)=>await s.z.user.update({where:{id:e},data:t}),updateKYCStatus:async(e,t)=>await s.z.user.update({where:{id:e},data:{kycStatus:t}})},i={async create(e){let t=new Date;return t.setFullYear(t.getFullYear()+1),await s.z.miningUnit.create({data:{userId:e.userId,thsAmount:e.thsAmount,investmentAmount:e.investmentAmount,dailyROI:e.dailyROI,expiryDate:t}})},findActiveByUserId:async e=>await s.z.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}}}),updateTotalEarned:async(e,t)=>await s.z.miningUnit.update({where:{id:e},data:{totalEarned:{increment:t}}}),expireUnit:async e=>await s.z.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}})},n={create:async e=>await s.z.transaction.create({data:{userId:e.userId,type:e.type,amount:e.amount,description:e.description,status:e.status||"PENDING"}}),async findByUserId(e,t){let a={userId:e};return t?.types&&t.types.length>0&&(a.type={in:t.types}),t?.status&&(a.status=t.status),await s.z.transaction.findMany({where:a,orderBy:{createdAt:"desc"},take:t?.limit||50,skip:t?.offset})},updateStatus:async(e,t)=>await s.z.transaction.update({where:{id:e},data:{status:t}})},d={create:async e=>await s.z.referral.create({data:{referrerId:e.referrerId,referredId:e.referredId,placementSide:e.placementSide}}),findByReferrerId:async e=>await s.z.referral.findMany({where:{referrerId:e},include:{referred:{select:{id:!0,email:!0,createdAt:!0}}}})},c={upsert:async e=>await s.z.binaryPoints.upsert({where:{userId:e.userId},update:{leftPoints:void 0!==e.leftPoints?{increment:e.leftPoints}:void 0,rightPoints:void 0!==e.rightPoints?{increment:e.rightPoints}:void 0},create:{userId:e.userId,leftPoints:e.leftPoints||0,rightPoints:e.rightPoints||0}}),findByUserId:async e=>await s.z.binaryPoints.findUnique({where:{userId:e}}),resetPoints:async(e,t,a)=>await s.z.binaryPoints.update({where:{userId:e},data:{leftPoints:t,rightPoints:a,flushDate:new Date}})},o={create:async e=>await s.z.withdrawalRequest.create({data:{userId:e.userId,amount:e.amount,usdtAddress:e.usdtAddress}}),findPending:async()=>await s.z.withdrawalRequest.findMany({where:{status:"PENDING"},include:{user:{select:{id:!0,email:!0,kycStatus:!0}}},orderBy:{createdAt:"asc"}}),updateStatus:async(e,t,a,r,i)=>await s.z.withdrawalRequest.update({where:{id:e},data:{status:t,processedBy:a,txid:r,rejectionReason:i,processedAt:new Date}})},u={async get(e){let t=await s.z.adminSettings.findUnique({where:{key:e}});return t?.value},set:async(e,t,a)=>await s.z.adminSettings.upsert({where:{key:e},update:{value:t,updatedBy:a},create:{key:e,value:t,updatedBy:a}}),getAll:async()=>await s.z.adminSettings.findMany()},l={create:async e=>await s.z.systemLog.create({data:{action:e.action,userId:e.userId,adminId:e.adminId,details:e.details?JSON.stringify(e.details):null,ipAddress:e.ipAddress,userAgent:e.userAgent}})},p={async getOrCreate(e){let t=await s.z.walletBalance.findUnique({where:{userId:e}});return t||(t=await s.z.walletBalance.create({data:{userId:e,availableBalance:0,pendingBalance:0,totalDeposits:0,totalWithdrawals:0,totalEarnings:0}})),t},updateBalance:async(e,t)=>await s.z.walletBalance.update({where:{userId:e},data:{...t,lastUpdated:new Date}}),async addDeposit(e,t){let a=await this.getOrCreate(e);return await s.z.walletBalance.update({where:{userId:e},data:{availableBalance:a.availableBalance+t,totalDeposits:a.totalDeposits+t,lastUpdated:new Date}})},async addEarnings(e,t){let a=await this.getOrCreate(e);return await s.z.walletBalance.update({where:{userId:e},data:{availableBalance:a.availableBalance+t,totalEarnings:a.totalEarnings+t,lastUpdated:new Date}})},async deductWithdrawal(e,t){let a=await this.getOrCreate(e);if(a.availableBalance<t)throw Error("Insufficient balance");return await s.z.walletBalance.update({where:{userId:e},data:{availableBalance:a.availableBalance-t,totalWithdrawals:a.totalWithdrawals+t,lastUpdated:new Date}})},async findByUserId(e){return await this.getOrCreate(e)}},w={create:async e=>await s.z.depositTransaction.create({data:{userId:e.userId,transactionId:e.transactionId,amount:e.amount,usdtAmount:e.usdtAmount,tronAddress:e.tronAddress,senderAddress:e.senderAddress,blockNumber:e.blockNumber,blockTimestamp:e.blockTimestamp,confirmations:e.confirmations||0,status:"PENDING"}}),findByTransactionId:async e=>await s.z.depositTransaction.findUnique({where:{transactionId:e},include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}}),async findByUserId(e,t){let a={userId:e};return t?.status&&(a.status=t.status),await s.z.depositTransaction.findMany({where:a,orderBy:{createdAt:"desc"},take:t?.limit||50,skip:t?.offset,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}})},async findAll(e){let t={};return e?.status&&(t.status=e.status),await s.z.depositTransaction.findMany({where:t,orderBy:{createdAt:"desc"},take:e?.limit||100,skip:e?.offset,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}})},async updateStatus(e,t,a){let r={status:t};return a?.verifiedAt&&(r.verifiedAt=a.verifiedAt),a?.processedAt&&(r.processedAt=a.processedAt),a?.failureReason&&(r.failureReason=a.failureReason),a?.confirmations!==void 0&&(r.confirmations=a.confirmations),await s.z.depositTransaction.update({where:{transactionId:e},data:r})},async markAsCompleted(e){return await this.updateStatus(e,"COMPLETED",{processedAt:new Date})},async markAsFailed(e,t){return await this.updateStatus(e,"FAILED",{failureReason:t,processedAt:new Date})},async getPendingDeposits(){return await this.findAll({status:"PENDING"})},async getDepositStats(){let e=await s.z.depositTransaction.aggregate({_count:{id:!0},_sum:{usdtAmount:!0},where:{status:"COMPLETED"}}),t=await s.z.depositTransaction.count({where:{status:"PENDING"}});return{totalDeposits:e._count.id||0,totalAmount:e._sum.usdtAmount||0,pendingDeposits:t}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,a)=>{"use strict";a.d(t,{z:()=>r});var s=a(96330);let r=globalThis.prisma??new s.PrismaClient},43286:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>y,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>w});var s={};a.r(s),a.d(s,{POST:()=>u});var r=a(96559),i=a(48088),n=a(37719),d=a(32190),c=a(2746),o=a(6710);async function u(e){try{let t=e.headers.get("authorization"),a=process.env.CRON_SECRET||"default-secret";if(t!==`Bearer ${a}`)return d.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});console.log("Starting binary matching cron job...");let s=await (0,c.E5)(),r=s.reduce((e,t)=>e+t.payout,0),i=s.reduce((e,t)=>e+t.matchedPoints,0);return await o.AJ.create({action:"BINARY_MATCHING_CRON_EXECUTED",details:{usersProcessed:s.length,totalPayouts:r,totalMatchedPoints:i,executionTime:new Date().toISOString(),matchingTime:"12:00 AM UTC"}}),d.NextResponse.json({success:!0,message:"Binary matching completed",data:{usersProcessed:s.length,totalPayouts:r,totalMatchedPoints:i}})}catch(e){return console.error("Binary matching cron job error:",e),await o.AJ.create({action:"BINARY_MATCHING_CRON_ERROR",details:{error:e.message,stack:e.stack,timestamp:new Date().toISOString()}}),d.NextResponse.json({success:!1,error:"Binary matching failed"},{status:500})}}let l=new r.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/cron/binary-matching/route",pathname:"/api/cron/binary-matching",filename:"route",bundlePath:"app/api/cron/binary-matching/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\binary-matching\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:w,serverHooks:m}=l;function y(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:w})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,580,2746],()=>a(43286));module.exports=s})();