import { NextRequest, NextResponse } from 'next/server';
import { adminSettingsDb } from '@/lib/database';

// GET - Fetch pricing settings (public endpoint for frontend)
export async function GET(request: NextRequest) {
  try {
    // Get pricing settings from database
    const thsPrice = await adminSettingsDb.get('THS_PRICE') || '50';
    const roiMin = await adminSettingsDb.get('ROI_MIN') || '0.6';
    const roiMax = await adminSettingsDb.get('ROI_MAX') || '1.1';
    const minimumPurchase = await adminSettingsDb.get('MINIMUM_PURCHASE') || '50';

    const pricingData = {
      thsPrice: parseFloat(thsPrice),
      roiRange: {
        min: parseFloat(roiMin),
        max: parseFloat(roiMax),
      },
      minimumPurchase: parseFloat(minimumPurchase),
    };

    return NextResponse.json({
      success: true,
      data: pricingData,
    });

  } catch (error: any) {
    console.error('Pricing fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch pricing settings' },
      { status: 500 }
    );
  }
}
