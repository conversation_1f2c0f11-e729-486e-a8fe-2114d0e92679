import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { miningUnitDb, transactionDb, adminSettingsDb, systemLogDb } from '@/lib/database';

// GET - Fetch user's mining units
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const miningUnits = await miningUnitDb.findActiveByUserId(user.id);

    return NextResponse.json({
      success: true,
      data: miningUnits,
    });

  } catch (error: any) {
    console.error('Mining units fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch mining units' },
      { status: 500 }
    );
  }
}

// POST - Purchase new mining unit
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { thsAmount, investmentAmount } = body;

    // Validation
    if (!thsAmount || !investmentAmount || thsAmount <= 0 || investmentAmount <= 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid TH/s amount or investment amount' },
        { status: 400 }
      );
    }

    // Get minimum purchase amount from admin settings
    const minPurchase = parseFloat(await adminSettingsDb.get('MINIMUM_PURCHASE') || '50');
    if (investmentAmount < minPurchase) {
      return NextResponse.json(
        { success: false, error: `Minimum purchase amount is $${minPurchase}` },
        { status: 400 }
      );
    }

    // Get TH/s price from admin settings
    const thsPrice = parseFloat(await adminSettingsDb.get('THS_PRICE') || '50');
    const expectedAmount = thsAmount * thsPrice;
    
    // Allow small rounding differences (within 1%)
    if (Math.abs(investmentAmount - expectedAmount) > expectedAmount * 0.01) {
      return NextResponse.json(
        { success: false, error: 'Investment amount does not match TH/s price' },
        { status: 400 }
      );
    }

    // Get ROI range from admin settings
    const roiMin = parseFloat(await adminSettingsDb.get('ROI_MIN') || '0.6');
    const roiMax = parseFloat(await adminSettingsDb.get('ROI_MAX') || '1.1');
    
    // Generate random daily ROI within range
    const dailyROI = roiMin + Math.random() * (roiMax - roiMin);

    // Create mining unit
    const miningUnit = await miningUnitDb.create({
      userId: user.id,
      thsAmount,
      investmentAmount,
      dailyROI,
    });

    // Create purchase transaction
    await transactionDb.create({
      userId: user.id,
      type: 'PURCHASE',
      amount: investmentAmount,
      description: `Mining unit purchase - ${thsAmount} TH/s`,
      status: 'COMPLETED',
    });

    // Log the purchase
    await systemLogDb.create({
      action: 'MINING_UNIT_PURCHASED',
      userId: user.id,
      details: {
        miningUnitId: miningUnit.id,
        thsAmount,
        investmentAmount,
        dailyROI,
      },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json({
      success: true,
      message: 'Mining unit purchased successfully',
      data: miningUnit,
    });

  } catch (error: any) {
    console.error('Mining unit purchase error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to purchase mining unit' },
      { status: 500 }
    );
  }
}
