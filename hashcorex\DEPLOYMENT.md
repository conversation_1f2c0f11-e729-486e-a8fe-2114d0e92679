# HashCoreX Deployment Guide

This guide covers the complete deployment process for the HashCoreX platform.

## Prerequisites

- Node.js 18+ and npm/yarn
- PostgreSQL database
- Domain name and SSL certificate
- Server with at least 2GB RAM and 20GB storage

## Environment Setup

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd hashcorex
npm install
```

### 2. Environment Configuration

Copy the example environment file:
```bash
cp .env.example .env.local
```

Configure the following variables in `.env.local`:

#### Database Configuration
```env
DATABASE_URL="****************************************/hashcorex"
DIRECT_URL="****************************************/hashcorex"
```

#### Security Keys
```env
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
ENCRYPTION_KEY="your-32-character-encryption-key-here"
```

#### Admin Configuration
```env
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="secure-admin-password"
```

#### Production URLs
```env
NODE_ENV="production"
NEXT_PUBLIC_APP_URL="https://yourdomain.com"
```

### 3. Database Setup

#### Create Database
```sql
CREATE DATABASE hashcorex;
CREATE USER hashcorex_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE hashcorex TO hashcorex_user;
```

#### Run Migrations
```bash
npx prisma generate
npx prisma db push
```

#### Seed Initial Data
```bash
npm run seed
```

## Deployment Options

### Option 1: Vercel Deployment (Recommended)

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Deploy to Vercel**
   ```bash
   vercel --prod
   ```

3. **Configure Environment Variables**
   - Go to Vercel Dashboard → Project → Settings → Environment Variables
   - Add all variables from your `.env.local`

4. **Configure Database**
   - Use Vercel Postgres or external PostgreSQL service
   - Update `DATABASE_URL` in Vercel environment variables

### Option 2: Docker Deployment

1. **Build Docker Image**
   ```bash
   docker build -t hashcorex .
   ```

2. **Run with Docker Compose**
   ```bash
   docker-compose up -d
   ```

### Option 3: VPS Deployment

1. **Install Dependencies on Server**
   ```bash
   # Install Node.js
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # Install PM2
   npm install -g pm2
   ```

2. **Deploy Application**
   ```bash
   # Build application
   npm run build
   
   # Start with PM2
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

3. **Configure Nginx**
   ```nginx
   server {
       listen 80;
       server_name yourdomain.com;
       return 301 https://$server_name$request_uri;
   }
   
   server {
       listen 443 ssl;
       server_name yourdomain.com;
       
       ssl_certificate /path/to/certificate.crt;
       ssl_certificate_key /path/to/private.key;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

## Cron Jobs Setup

The platform requires automated cron jobs for:
- Daily ROI calculations
- Weekly earnings distribution
- Binary matching

### Setup Cron Jobs

1. **Create cron script**
   ```bash
   # /opt/hashcorex/cron.sh
   #!/bin/bash
   
   CRON_SECRET="your-cron-secret-key"
   BASE_URL="https://yourdomain.com"
   
   # Daily ROI calculation (runs daily at 00:01)
   curl -X POST "$BASE_URL/api/cron/daily-roi" \
        -H "Authorization: Bearer $CRON_SECRET"
   
   # Weekly payout (runs Saturday at 15:00 UTC)
   if [ $(date +%u) -eq 6 ] && [ $(date +%H) -eq 15 ]; then
       curl -X POST "$BASE_URL/api/cron/weekly-payout" \
            -H "Authorization: Bearer $CRON_SECRET"
   fi
   
   # Binary matching (runs daily at 00:00 UTC)
   curl -X POST "$BASE_URL/api/cron/binary-matching" \
        -H "Authorization: Bearer $CRON_SECRET"
   ```

2. **Add to crontab**
   ```bash
   # Edit crontab
   crontab -e
   
   # Add these lines:
   # Daily ROI calculation at 00:01 UTC
   1 0 * * * /opt/hashcorex/cron.sh
   
   # Binary matching at 00:00 UTC
   0 0 * * * /opt/hashcorex/cron.sh
   
   # Weekly payout on Saturday at 15:00 UTC
   0 15 * * 6 /opt/hashcorex/cron.sh
   ```

## Security Checklist

### Environment Security
- [ ] Change all default passwords
- [ ] Use strong JWT secrets (32+ characters)
- [ ] Enable HTTPS/SSL
- [ ] Configure CORS properly
- [ ] Set secure cookie settings

### Database Security
- [ ] Use strong database passwords
- [ ] Restrict database access to application only
- [ ] Enable database SSL
- [ ] Regular database backups

### Application Security
- [ ] Enable rate limiting
- [ ] Configure CSP headers
- [ ] Validate all user inputs
- [ ] Sanitize file uploads
- [ ] Monitor for suspicious activities

## Monitoring and Maintenance

### Health Checks
```bash
# Check application status
curl https://yourdomain.com/api/health

# Check database connection
npm run db:check
```

### Log Monitoring
```bash
# PM2 logs
pm2 logs

# Application logs
tail -f logs/application.log

# Error logs
tail -f logs/error.log
```

### Database Maintenance
```bash
# Backup database
pg_dump hashcorex > backup_$(date +%Y%m%d).sql

# Optimize database
VACUUM ANALYZE;
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check DATABASE_URL format
   - Verify database credentials
   - Ensure database server is running

2. **Build Failures**
   - Clear node_modules and reinstall
   - Check Node.js version compatibility
   - Verify all environment variables

3. **Cron Jobs Not Running**
   - Check cron service status
   - Verify CRON_SECRET matches
   - Check server timezone settings

### Performance Optimization

1. **Database Optimization**
   - Add indexes for frequently queried fields
   - Use connection pooling
   - Regular VACUUM operations

2. **Application Optimization**
   - Enable Next.js caching
   - Use CDN for static assets
   - Implement Redis for session storage

3. **Server Optimization**
   - Configure proper memory limits
   - Use process clustering
   - Monitor resource usage

## Support

For deployment support and issues:
- Check the troubleshooting section
- Review application logs
- Contact technical support

## Security Updates

Regularly update:
- Node.js and npm packages
- Database software
- Server operating system
- SSL certificates

Monitor security advisories and apply patches promptly.
