"use strict";(()=>{var e={};e.id=8483,e.ids=[8483],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7355:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>f,serverHooks:()=>y,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{POST:()=>m});var a=r(96559),o=r(48088),u=r(37719),n=r(32190),i=r(12909),d=r(6710),p=r(31183);let c=require("fs/promises");var l=r(33873);async function m(e){try{let t,{authenticated:r,user:s}=await (0,i.b9)(e);if(!r||!s)return n.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let a=await e.formData(),o=a.get("file"),u=a.get("documentType");if(!o)return n.NextResponse.json({success:!1,error:"No file provided"},{status:400});if(!u||!["ID","SELFIE"].includes(u))return n.NextResponse.json({success:!1,error:"Invalid document type"},{status:400});if(!o.type.startsWith("image/"))return n.NextResponse.json({success:!1,error:"Only image files are allowed"},{status:400});if(o.size>5242880)return n.NextResponse.json({success:!1,error:"File size must be less than 5MB"},{status:400});let m=(0,l.join)(process.cwd(),"public","uploads","kyc");try{await (0,c.mkdir)(m,{recursive:!0})}catch(e){}let f=o.name.split(".").pop(),x=`${s.id}_${u}_${Object(function(){var e=Error("Cannot find module 'uuid'");throw e.code="MODULE_NOT_FOUND",e}())()}.${f}`,w=(0,l.join)(m,x),y=`/uploads/kyc/${x}`,h=await o.arrayBuffer(),k=Buffer.from(h);await (0,c.writeFile)(w,k);let v=await p.z.kYCDocument.findFirst({where:{userId:s.id,documentType:u}});t=v?await p.z.kYCDocument.update({where:{id:v.id},data:{filePath:y,status:"PENDING",reviewedAt:null,reviewedBy:null,rejectionReason:null}}):await p.z.kYCDocument.create({data:{userId:s.id,documentType:u,filePath:y,status:"PENDING"}});let N=await p.z.kYCDocument.findMany({where:{userId:s.id}}),g=N.some(e=>"ID"===e.documentType),D=N.some(e=>"SELFIE"===e.documentType);return g&&D&&await p.z.user.update({where:{id:s.id},data:{kycStatus:"PENDING"}}),await d.AJ.create({action:"KYC_DOCUMENT_UPLOADED",userId:s.id,details:{documentType:u,fileName:x,fileSize:o.size,documentId:t.id},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),n.NextResponse.json({success:!0,message:"Document uploaded successfully",data:{documentId:t.id,documentType:t.documentType,status:t.status}})}catch(e){return console.error("KYC document upload error:",e),n.NextResponse.json({success:!1,error:"Failed to upload document"},{status:500})}}!function(){var e=Error("Cannot find module 'uuid'");throw e.code="MODULE_NOT_FOUND",e}();let f=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/kyc/upload/route",pathname:"/api/kyc/upload",filename:"route",bundlePath:"app/api/kyc/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\upload\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:w,serverHooks:y}=f;function h(){return(0,u.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:w})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3529],()=>r(7355));module.exports=s})();