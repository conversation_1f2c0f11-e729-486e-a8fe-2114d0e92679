"use strict";(()=>{var e={};e.id=8967,e.ids=[8967],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21143:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>c});var a=t(96559),o=t(48088),n=t(37719),i=t(32190),u=t(12909),p=t(2746);async function c(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return i.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{searchParams:s}=new URL(e.url),a=s.get("term"),o=parseInt(s.get("limit")||"20");if(!a||a.trim().length<2)return i.NextResponse.json({success:!1,error:"Search term must be at least 2 characters"},{status:400});let n=await (0,p.gj)(t.id,a.trim(),o);return i.NextResponse.json({success:!0,data:n})}catch(e){return console.error("Binary tree search error:",e),i.NextResponse.json({success:!1,error:"Failed to search binary tree"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/referrals/search/route",pathname:"/api/referrals/search",filename:"route",bundlePath:"app/api/referrals/search/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\referrals\\search\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:h}=d;function m(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3529,2746],()=>t(21143));module.exports=s})();