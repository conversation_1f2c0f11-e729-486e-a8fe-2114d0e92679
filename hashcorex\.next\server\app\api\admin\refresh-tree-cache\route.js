"use strict";(()=>{var e={};e.id=4413,e.ids=[4413],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16034:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>h,serverHooks:()=>m,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>d});var a=t(96559),n=t(48088),o=t(37719),i=t(32190),p=t(12909),u=t(2746),c=t(31183);async function d(e){try{let{authenticated:r,user:t}=await (0,p.b9)(e);if(!r||!t)return i.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let s=(await c.z.user.findMany({select:{id:!0}})).map(e=>e.id);return await (0,u.Yn)(s),i.NextResponse.json({success:!0,message:`Updated tree cache for ${s.length} users`})}catch(e){return console.error("Refresh tree cache error:",e),i.NextResponse.json({success:!1,error:"Failed to refresh tree cache"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/refresh-tree-cache/route",pathname:"/api/admin/refresh-tree-cache",filename:"route",bundlePath:"app/api/admin/refresh-tree-cache/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\refresh-tree-cache\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:l,serverHooks:m}=h;function f(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:l})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3529,2746],()=>t(16034));module.exports=s})();