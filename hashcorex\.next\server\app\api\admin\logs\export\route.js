"use strict";(()=>{var e={};e.id=1776,e.ids=[1776],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38974:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>u});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),p=r(12909),d=r(31183);async function u(e){try{let{authenticated:t,user:r}=await (0,p.b9)(e);if(!t||!r)return i.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,p.qc)(r.id))return i.NextResponse.json({error:"Admin access required"},{status:403});let{searchParams:s}=new URL(e.url),a=s.get("search")||"",n=s.get("action")||"all",o=s.get("dateRange")||"today",u={};a&&(u.OR=[{action:{contains:a,mode:"insensitive"}},{details:{contains:a,mode:"insensitive"}},{ipAddress:{contains:a,mode:"insensitive"}}]),"all"!==n&&(u.action=n);let c=new Date;switch(o){case"today":u.createdAt={gte:new Date(c.getFullYear(),c.getMonth(),c.getDate())};break;case"week":u.createdAt={gte:new Date(c.getTime()-6048e5)};break;case"month":u.createdAt={gte:new Date(c.getFullYear(),c.getMonth(),1)}}let l=(await d.z.systemLog.findMany({where:u,include:{user:{select:{firstName:!0,lastName:!0,email:!0}}},orderBy:{createdAt:"desc"}})).map(e=>[e.createdAt.toISOString(),e.action,e.user?`${e.user.firstName} ${e.user.lastName}`:"System",e.user?.email||"N/A",e.details||"",e.ipAddress||"",e.userAgent||""]),m=["Date,Action,User,Email,Details,IP Address,User Agent",...l.map(e=>e.map(e=>`"${String(e).replace(/"/g,'""')}"`).join(","))].join("\n");return new i.NextResponse(m,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="system-logs-${new Date().toISOString().split("T")[0]}.csv"`}})}catch(e){return console.error("Admin logs export error:",e),i.NextResponse.json({success:!1,error:"Failed to export logs"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/logs/export/route",pathname:"/api/admin/logs/export",filename:"route",bundlePath:"app/api/admin/logs/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\logs\\export\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:g}=c;function x(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3529],()=>r(38974));module.exports=s})();