"use strict";(()=>{var e={};e.id=8225,e.ids=[8225],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},60864:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>c,PUT:()=>p});var a=r(96559),n=r(48088),i=r(37719),o=r(32190),u=r(12909),d=r(6710);async function c(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let s=await d.rs.getAll(),a={};s.forEach(e=>{try{a[e.key]=JSON.parse(e.value)}catch{a[e.key]=e.value}});let n={thsPriceUSD:50,minPurchaseAmount:100,maxPurchaseAmount:1e4,dailyROIPercentage:1.5,binaryBonusPercentage:10,referralBonusPercentage:5,usdtDepositAddress:"",minDepositAmount:10,maxDepositAmount:1e4,depositEnabled:!0,minConfirmations:1,depositFeePercentage:0,minWithdrawalAmount:50,withdrawalFeePercentage:2,withdrawalProcessingDays:3,platformFeePercentage:1,maintenanceMode:!1,registrationEnabled:!0,kycRequired:!0,...a};return o.NextResponse.json({success:!0,data:n})}catch(e){return console.error("Admin settings fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch settings"},{status:500})}}async function p(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let s=await e.json(),a=Object.entries(s).map(([e,t])=>d.rs.set(e,JSON.stringify(t),r.id));return await Promise.all(a),await d.AJ.create({action:"SYSTEM_SETTINGS_UPDATED",userId:r.id,details:{updatedSettings:Object.keys(s)},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Settings updated successfully"})}catch(e){return console.error("Admin settings update error:",e),o.NextResponse.json({success:!1,error:"Failed to update settings"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/settings/route",pathname:"/api/admin/settings",filename:"route",bundlePath:"app/api/admin/settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\settings\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:x}=l;function h(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3529],()=>r(60864));module.exports=s})();