'use client';

import React, { useState, useEffect } from 'react';
import { Button, Input, Card, CardHeader, CardTitle, CardContent, Modal } from '@/components/ui';
import { Grid } from '@/components/layout';
import { Wallet, ArrowUpRight, ArrowDownLeft, Clock, CheckCircle, XCircle, Copy } from 'lucide-react';
import { formatCurrency, formatDateTime, copyToClipboard } from '@/lib/utils';

interface WalletData {
  balance: number;
  pendingEarnings: number;
  recentTransactions: Array<{
    id: string;
    type: string;
    amount: number;
    description: string;
    status: string;
    createdAt: string;
  }>;
}

interface WithdrawalHistory {
  id: string;
  amount: number;
  usdtAddress: string;
  status: string;
  txid?: string;
  createdAt: string;
  processedAt?: string;
  rejectionReason?: string;
}

export const WalletDashboard: React.FC = () => {
  const [walletData, setWalletData] = useState<WalletData | null>(null);
  const [withdrawalHistory, setWithdrawalHistory] = useState<WithdrawalHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [withdrawalForm, setWithdrawalForm] = useState({
    amount: '',
    usdtAddress: '',
  });
  const [withdrawalLoading, setWithdrawalLoading] = useState(false);
  const [withdrawalError, setWithdrawalError] = useState('');
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [depositAddress, setDepositAddress] = useState('');

  useEffect(() => {
    fetchWalletData();
    fetchWithdrawalHistory();
    fetchDepositAddress();
  }, []);

  const fetchWalletData = async () => {
    try {
      const response = await fetch('/api/wallet/balance', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setWalletData(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch wallet data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchWithdrawalHistory = async () => {
    try {
      const response = await fetch('/api/wallet/withdraw', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setWithdrawalHistory(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch withdrawal history:', error);
    }
  };

  const fetchDepositAddress = async () => {
    try {
      const response = await fetch('/api/wallet/deposit-address', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setDepositAddress(data.data.address);
        }
      }
    } catch (error) {
      console.error('Failed to fetch deposit address:', error);
    }
  };

  const handleWithdrawal = async (e: React.FormEvent) => {
    e.preventDefault();
    setWithdrawalError('');
    setWithdrawalLoading(true);

    try {
      const amount = parseFloat(withdrawalForm.amount);
      
      if (!amount || amount <= 0) {
        throw new Error('Please enter a valid amount');
      }

      if (!withdrawalForm.usdtAddress) {
        throw new Error('Please enter a USDT address');
      }

      const response = await fetch('/api/wallet/withdraw', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          amount,
          usdtAddress: withdrawalForm.usdtAddress,
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Withdrawal failed');
      }

      // Reset form and close modal
      setWithdrawalForm({ amount: '', usdtAddress: '' });
      setShowWithdrawModal(false);
      
      // Refresh data
      fetchWalletData();
      fetchWithdrawalHistory();

    } catch (err: any) {
      setWithdrawalError(err.message || 'Withdrawal failed');
    } finally {
      setWithdrawalLoading(false);
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'WITHDRAWAL':
        return <ArrowUpRight className="h-4 w-4 text-red-500" />;
      case 'DEPOSIT':
      case 'MINING_EARNINGS':
      case 'DIRECT_REFERRAL':
      case 'BINARY_BONUS':
        return <ArrowDownLeft className="h-4 w-4 text-eco-500" />;
      default:
        return <Wallet className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'WITHDRAWAL':
      case 'PURCHASE':
        return 'text-red-600';
      case 'DEPOSIT':
      case 'MINING_EARNINGS':
      case 'DIRECT_REFERRAL':
      case 'BINARY_BONUS':
        return 'text-eco-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
      case 'APPROVED':
        return <CheckCircle className="h-4 w-4 text-eco-500" />;
      case 'PENDING':
        return <Clock className="h-4 w-4 text-solar-500" />;
      case 'FAILED':
      case 'REJECTED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const handleCopyAddress = async (address: string) => {
    try {
      await copyToClipboard(address);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy address:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-32 bg-gray-200 rounded-xl"></div>
        </div>
      </div>
    );
  }

  if (!walletData) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">Failed to load wallet data</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      {/* Wallet Overview */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Wallet Overview</h2>
        <Grid cols={{ default: 1, lg: 2 }} gap={8}>
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Available Balance</p>
                  <p className="text-4xl font-bold text-dark-900">
                    {formatCurrency(walletData.balance)}
                  </p>
                </div>
                <div className="h-16 w-16 bg-eco-100 rounded-xl flex items-center justify-center">
                  <Wallet className="h-8 w-8 text-eco-600" />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  onClick={() => setShowDepositModal(true)}
                  variant="outline"
                  className="h-12 text-base font-semibold rounded-xl"
                >
                  <ArrowDownLeft className="h-5 w-5 mr-2" />
                  Deposit
                </Button>
                <Button
                  onClick={() => setShowWithdrawModal(true)}
                  className="h-12 text-base font-semibold rounded-xl"
                  disabled={walletData.balance < 10}
                >
                  <ArrowUpRight className="h-5 w-5 mr-2" />
                  Withdraw
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Pending Earnings</p>
                  <p className="text-4xl font-bold text-solar-600">
                    {formatCurrency(walletData.pendingEarnings)}
                  </p>
                </div>
                <div className="h-16 w-16 bg-solar-100 rounded-xl flex items-center justify-center">
                  <Clock className="h-8 w-8 text-solar-600" />
                </div>
              </div>
              <p className="text-sm text-gray-600 font-medium">
                Will be transferred on Saturday at 15:00 UTC
              </p>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          {walletData.recentTransactions.length > 0 ? (
            <div className="space-y-3">
              {walletData.recentTransactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getTransactionIcon(transaction.type)}
                    <div>
                      <p className="font-medium text-dark-900">{transaction.description}</p>
                      <p className="text-sm text-gray-500">{formatDateTime(transaction.createdAt)}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(transaction.status)}
                    <span className={`font-semibold ${getTransactionColor(transaction.type)}`}>
                      {transaction.type === 'WITHDRAWAL' || transaction.type === 'PURCHASE' ? '-' : '+'}
                      {formatCurrency(transaction.amount)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No transactions yet</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Withdrawal History */}
      {withdrawalHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Withdrawal History</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {withdrawalHistory.map((withdrawal) => (
                <div key={withdrawal.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-dark-900">
                        {formatCurrency(withdrawal.amount)}
                      </span>
                      {getStatusIcon(withdrawal.status)}
                      <span className={`text-sm px-2 py-1 rounded-full ${
                        withdrawal.status === 'COMPLETED' ? 'bg-eco-100 text-eco-700' :
                        withdrawal.status === 'PENDING' ? 'bg-solar-100 text-solar-700' :
                        'bg-red-100 text-red-700'
                      }`}>
                        {withdrawal.status}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 mt-1">
                      <p className="text-sm text-gray-600">
                        To: {withdrawal.usdtAddress.slice(0, 8)}...{withdrawal.usdtAddress.slice(-8)}
                      </p>
                      <button
                        onClick={() => handleCopyAddress(withdrawal.usdtAddress)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <Copy className="h-3 w-3" />
                      </button>
                    </div>
                    <p className="text-xs text-gray-500">{formatDateTime(withdrawal.createdAt)}</p>
                    {withdrawal.txid && (
                      <p className="text-xs text-eco-600 mt-1">
                        TXID: {withdrawal.txid.slice(0, 16)}...
                      </p>
                    )}
                    {withdrawal.rejectionReason && (
                      <p className="text-xs text-red-600 mt-1">
                        Reason: {withdrawal.rejectionReason}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Withdrawal Modal */}
      <Modal
        isOpen={showWithdrawModal}
        onClose={() => setShowWithdrawModal(false)}
        title="Withdraw USDT"
      >
        <form onSubmit={handleWithdrawal} className="space-y-4">
          {withdrawalError && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
              {withdrawalError}
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg text-sm">
            <p><strong>Available Balance:</strong> {formatCurrency(walletData.balance)}</p>
            <p><strong>Minimum Withdrawal:</strong> $10.00</p>
            <p><strong>Network:</strong> USDT (TRC20)</p>
          </div>

          <Input
            label="Withdrawal Amount (USD)"
            type="number"
            step="0.01"
            min="10"
            max={walletData.balance}
            value={withdrawalForm.amount}
            onChange={(e) => setWithdrawalForm(prev => ({ ...prev, amount: e.target.value }))}
            placeholder="Enter amount to withdraw"
            required
          />

          <Input
            label="USDT TRC20 Address"
            type="text"
            value={withdrawalForm.usdtAddress}
            onChange={(e) => setWithdrawalForm(prev => ({ ...prev, usdtAddress: e.target.value }))}
            placeholder="Enter your USDT TRC20 address"
            required
          />

          <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg text-sm">
            <p><strong>Important:</strong></p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>Only USDT TRC20 addresses are supported</li>
              <li>Withdrawals require KYC verification</li>
              <li>Processing time: 1-3 business days</li>
              <li>Double-check your address - transactions cannot be reversed</li>
            </ul>
          </div>

          <div className="flex space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowWithdrawModal(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              loading={withdrawalLoading}
              className="flex-1"
            >
              Submit Withdrawal
            </Button>
          </div>
        </form>
      </Modal>

      {/* Deposit Modal */}
      <Modal
        isOpen={showDepositModal}
        onClose={() => setShowDepositModal(false)}
        title="Deposit USDT"
      >
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg text-sm">
            <p><strong>Network:</strong> USDT (TRC20)</p>
            <p><strong>Minimum Deposit:</strong> $10.00</p>
            <p><strong>Confirmation Time:</strong> 1-3 network confirmations</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Your Deposit Address
            </label>
            <div className="flex items-center space-x-2">
              <Input
                type="text"
                value={depositAddress}
                readOnly
                className="flex-1 bg-gray-50"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => handleCopyAddress(depositAddress)}
                className="px-3"
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg text-sm">
            <p><strong>Important:</strong></p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>Only send USDT (TRC20) to this address</li>
              <li>Sending other tokens may result in permanent loss</li>
              <li>Deposits are automatically credited after confirmation</li>
              <li>Contact support if your deposit doesn't appear within 24 hours</li>
            </ul>
          </div>

          <Button
            onClick={() => setShowDepositModal(false)}
            className="w-full"
          >
            Close
          </Button>
        </div>
      </Modal>
    </div>
  );
};
