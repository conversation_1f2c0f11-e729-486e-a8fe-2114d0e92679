'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui';
import { Container, Grid, Flex } from '@/components/layout';
import { SolarPanel, Leaf } from '@/components/icons';

interface PublicLayoutProps {
  children: React.ReactNode;
}

export const PublicLayout: React.FC<PublicLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-white">
      {/* Premium Navigation */}
      <nav className="fixed top-0 w-full z-50 glass-morphism border-b border-white/20">
        <Container>
          <Flex justify="between" align="center" className="h-20">
            <Link href="/" className="flex items-center space-x-3">
              <div className="relative">
                <SolarPanel className="h-10 w-10 text-yellow-500 animate-pulse" />
                <div className="absolute inset-0 bg-yellow-500/20 rounded-full animate-ping"></div>
              </div>
              <span className="text-3xl font-black text-slate-900">
                HashCoreX
              </span>
            </Link>
            <Flex align="center" gap={8}>
              <Link href="/about" className="text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105">
                About
              </Link>
              <Link href="/how-it-works" className="text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105">
                How It Works
              </Link>
              <Link href="/login">
                <Button variant="ghost" size="md" className="font-semibold">
                  Login
                </Button>
              </Link>
              <Link href="/register">
                <Button variant="primary" size="md" className="font-semibold">
                  Get Started
                </Button>
              </Link>
            </Flex>
          </Flex>
        </Container>
      </nav>

      {/* Main Content */}
      <main className="pt-20">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-dark-900 text-white py-12">
        <Container>
          <Grid cols={{ default: 1, md: 4 }} gap={8}>
            <div>
              <Flex align="center" gap={2} className="mb-4">
                <SolarPanel className="h-8 w-8 text-yellow-400" />
                <span className="text-2xl font-bold">HashCoreX</span>
              </Flex>
              <p className="text-gray-400">
                Sustainable cryptocurrency mining powered by renewable energy.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Platform</h4>
              <div className="space-y-2">
                <Link href="/about" className="block text-gray-400 hover:text-white transition-colors">
                  About Us
                </Link>
                <Link href="/how-it-works" className="block text-gray-400 hover:text-white transition-colors">
                  How It Works
                </Link>
                <Link href="/pricing" className="block text-gray-400 hover:text-white transition-colors">
                  Pricing
                </Link>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <div className="space-y-2">
                <Link href="/contact" className="block text-gray-400 hover:text-white transition-colors">
                  Contact Us
                </Link>
                <Link href="/faq" className="block text-gray-400 hover:text-white transition-colors">
                  FAQ
                </Link>
                <Link href="/help" className="block text-gray-400 hover:text-white transition-colors">
                  Help Center
                </Link>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Legal</h4>
              <div className="space-y-2">
                <Link href="/privacy" className="block text-gray-400 hover:text-white transition-colors">
                  Privacy Policy
                </Link>
                <Link href="/terms" className="block text-gray-400 hover:text-white transition-colors">
                  Terms of Service
                </Link>
                <Link href="/compliance" className="block text-gray-400 hover:text-white transition-colors">
                  Compliance
                </Link>
              </div>
            </div>
          </Grid>
          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; 2024 HashCoreX. All rights reserved.</p>
          </div>
        </Container>
      </footer>
    </div>
  );
};
