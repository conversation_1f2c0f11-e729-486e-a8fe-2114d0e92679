import { prisma } from './prisma';
import { User, MiningUnit, Transaction, Referral, BinaryPoints } from '@/types';

// User Database Operations
export const userDb = {
  async create(data: {
    email: string;
    firstName: string;
    lastName: string;
    password: string;
    referralId?: string;
  }) {
    return await prisma.user.create({
      data: {
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        password: data.password,
        referralId: data.referralId || undefined,
      },
    });
  },

  async findByEmail(email: string) {
    return await prisma.user.findUnique({
      where: { email },
      include: {
        miningUnits: true,
        transactions: true,
        binaryPoints: true,
      },
    });
  },

  async findById(id: string) {
    return await prisma.user.findUnique({
      where: { id },
      include: {
        miningUnits: true,
        transactions: true,
        binaryPoints: true,
      },
    });
  },

  async findByReferralId(referralId: string) {
    return await prisma.user.findUnique({
      where: { referralId },
    });
  },

  async update(id: string, data: Partial<{
    firstName: string;
    lastName: string;
    email: string;
    role: 'USER' | 'ADMIN';
    isActive: boolean;
    kycStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  }>) {
    return await prisma.user.update({
      where: { id },
      data,
    });
  },

  async updateKYCStatus(userId: string, status: 'PENDING' | 'APPROVED' | 'REJECTED') {
    return await prisma.user.update({
      where: { id: userId },
      data: { kycStatus: status },
    });
  },
};

// Mining Unit Database Operations
export const miningUnitDb = {
  async create(data: {
    userId: string;
    thsAmount: number;
    investmentAmount: number;
    dailyROI: number;
  }) {
    const expiryDate = new Date();
    expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 12 months from now

    return await prisma.miningUnit.create({
      data: {
        userId: data.userId,
        thsAmount: data.thsAmount,
        investmentAmount: data.investmentAmount,
        dailyROI: data.dailyROI,
        expiryDate,
      },
    });
  },

  async findActiveByUserId(userId: string) {
    return await prisma.miningUnit.findMany({
      where: {
        userId,
        status: 'ACTIVE',
        expiryDate: {
          gt: new Date(),
        },
      },
    });
  },

  async updateTotalEarned(unitId: string, amount: number) {
    return await prisma.miningUnit.update({
      where: { id: unitId },
      data: {
        totalEarned: {
          increment: amount,
        },
      },
    });
  },

  async expireUnit(unitId: string) {
    return await prisma.miningUnit.update({
      where: { id: unitId },
      data: { status: 'EXPIRED' },
    });
  },
};

// Transaction Database Operations
export const transactionDb = {
  async create(data: {
    userId: string;
    type: 'MINING_EARNINGS' | 'DIRECT_REFERRAL' | 'BINARY_BONUS' | 'DEPOSIT' | 'WITHDRAWAL' | 'PURCHASE';
    amount: number;
    description: string;
    status?: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  }) {
    return await prisma.transaction.create({
      data: {
        userId: data.userId,
        type: data.type,
        amount: data.amount,
        description: data.description,
        status: data.status || 'PENDING',
      },
    });
  },

  async findByUserId(userId: string, filters?: {
    types?: string[];
    status?: string;
    limit?: number;
    offset?: number;
  }) {
    const where: any = { userId };

    if (filters?.types && filters.types.length > 0) {
      where.type = { in: filters.types };
    }

    if (filters?.status) {
      where.status = filters.status;
    }

    return await prisma.transaction.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: filters?.limit || 50,
      skip: filters?.offset,
    });
  },

  async updateStatus(transactionId: string, status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED') {
    return await prisma.transaction.update({
      where: { id: transactionId },
      data: { status },
    });
  },
};

// Referral Database Operations
export const referralDb = {
  async create(data: {
    referrerId: string;
    referredId: string;
    placementSide: 'LEFT' | 'RIGHT';
  }) {
    return await prisma.referral.create({
      data: {
        referrerId: data.referrerId,
        referredId: data.referredId,
        placementSide: data.placementSide,
      },
    });
  },

  async findByReferrerId(referrerId: string) {
    return await prisma.referral.findMany({
      where: { referrerId },
      include: {
        referred: {
          select: {
            id: true,
            email: true,
            createdAt: true,
          },
        },
      },
    });
  },
};

// Binary Points Database Operations
export const binaryPointsDb = {
  async upsert(data: {
    userId: string;
    leftPoints?: number;
    rightPoints?: number;
  }) {
    return await prisma.binaryPoints.upsert({
      where: { userId: data.userId },
      update: {
        leftPoints: data.leftPoints !== undefined ? { increment: data.leftPoints } : undefined,
        rightPoints: data.rightPoints !== undefined ? { increment: data.rightPoints } : undefined,
      },
      create: {
        userId: data.userId,
        leftPoints: data.leftPoints || 0,
        rightPoints: data.rightPoints || 0,
      },
    });
  },

  async findByUserId(userId: string) {
    return await prisma.binaryPoints.findUnique({
      where: { userId },
    });
  },

  async resetPoints(userId: string, leftPoints: number, rightPoints: number) {
    return await prisma.binaryPoints.update({
      where: { userId },
      data: {
        leftPoints,
        rightPoints,
        flushDate: new Date(),
      },
    });
  },
};

// Withdrawal Database Operations
export const withdrawalDb = {
  async create(data: {
    userId: string;
    amount: number;
    usdtAddress: string;
  }) {
    return await prisma.withdrawalRequest.create({
      data: {
        userId: data.userId,
        amount: data.amount,
        usdtAddress: data.usdtAddress,
      },
    });
  },

  async findPending() {
    return await prisma.withdrawalRequest.findMany({
      where: { status: 'PENDING' },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            kycStatus: true,
          },
        },
      },
      orderBy: { createdAt: 'asc' },
    });
  },

  async updateStatus(
    requestId: string, 
    status: 'APPROVED' | 'REJECTED' | 'COMPLETED',
    processedBy?: string,
    txid?: string,
    rejectionReason?: string
  ) {
    return await prisma.withdrawalRequest.update({
      where: { id: requestId },
      data: {
        status,
        processedBy,
        txid,
        rejectionReason,
        processedAt: new Date(),
      },
    });
  },
};

// Admin Settings Database Operations
export const adminSettingsDb = {
  async get(key: string) {
    const setting = await prisma.adminSettings.findUnique({
      where: { key },
    });
    return setting?.value;
  },

  async set(key: string, value: string, updatedBy?: string) {
    return await prisma.adminSettings.upsert({
      where: { key },
      update: { value, updatedBy },
      create: { key, value, updatedBy },
    });
  },

  async getAll() {
    return await prisma.adminSettings.findMany();
  },
};

// System Logs
export const systemLogDb = {
  async create(data: {
    action: string;
    userId?: string;
    adminId?: string;
    details?: any;
    ipAddress?: string;
    userAgent?: string;
  }) {
    return await prisma.systemLog.create({
      data: {
        action: data.action,
        userId: data.userId,
        adminId: data.adminId,
        details: data.details ? JSON.stringify(data.details) : null,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
      },
    });
  },
};
