"use strict";(()=>{var e={};e.id=906,e.ids=[906],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27822:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>p});var a=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(12909),d=t(31183);async function p(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,u.qc)(t.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let{searchParams:s}=new URL(e.url),a=s.get("search")||"",i=s.get("status")||"all",n={};a&&(n.OR=[{usdtAddress:{contains:a,mode:"insensitive"}},{user:{email:{contains:a,mode:"insensitive"}}},{user:{firstName:{contains:a,mode:"insensitive"}}},{user:{lastName:{contains:a,mode:"insensitive"}}}]),"all"!==i&&(n.status=i.toUpperCase());let p=await d.z.withdrawalRequest.findMany({where:n,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0,kycStatus:!0}}},orderBy:{createdAt:"desc"}});return o.NextResponse.json({success:!0,data:p})}catch(e){return console.error("Admin withdrawals fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch withdrawals"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/withdrawals/route",pathname:"/api/admin/withdrawals",filename:"route",bundlePath:"app/api/admin/withdrawals/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\withdrawals\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:x}=c;function w(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3529],()=>t(27822));module.exports=s})();