'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>H<PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent, Button, Input } from '@/components/ui';
import { 
  Shield, 
  Eye, 
  Check, 
  X, 
  Download, 
  FileText,
  User,
  Calendar,
  AlertTriangle
} from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface KYCDocument {
  id: string;
  userId: string;
  user: {
    firstName: string;
    lastName: string;
    email: string;
    referralId: string;
  };
  documentType: string;
  documentUrl: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  submittedAt: string;
  reviewedAt?: string;
  rejectionReason?: string;
}

export const KYCReview: React.FC = () => {
  const [documents, setDocuments] = useState<KYCDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDocument, setSelectedDocument] = useState<KYCDocument | null>(null);
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    fetchPendingKYC();
  }, []);

  const fetchPendingKYC = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/kyc/pending', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setDocuments(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch KYC documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReview = async (documentId: string, action: 'approve' | 'reject', reason?: string) => {
    try {
      setProcessing(true);
      const response = await fetch('/api/admin/kyc/review', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          documentId,
          action: action.toUpperCase(),
          rejectionReason: reason,
        }),
      });

      if (response.ok) {
        fetchPendingKYC(); // Refresh the list
        setSelectedDocument(null);
        setReviewAction(null);
        setRejectionReason('');
      }
    } catch (error) {
      console.error('Failed to review KYC document:', error);
    } finally {
      setProcessing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      PENDING: 'bg-yellow-900 text-yellow-300 border border-yellow-700',
      APPROVED: 'bg-green-900 text-green-300 border border-green-700',
      REJECTED: 'bg-red-900 text-red-300 border border-red-700',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[status as keyof typeof colors]}`}>
        {status}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-700 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-slate-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">KYC Review</h1>
          <p className="text-slate-400 mt-1">Review and approve user KYC documents</p>
        </div>
        <div className="flex items-center gap-2 text-sm text-slate-400">
          <AlertTriangle className="h-4 w-4 text-orange-400" />
          {documents.length} pending reviews
        </div>
      </div>

      {/* KYC Documents */}
      <div className="grid gap-6">
        {documents.length === 0 ? (
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-12 text-center">
              <Shield className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No Pending KYC Reviews</h3>
              <p className="text-slate-400">All KYC documents have been reviewed.</p>
            </CardContent>
          </Card>
        ) : (
          documents.map((doc) => (
            <Card key={doc.id} className="bg-slate-800 border-slate-700 hover:bg-slate-750 transition-colors">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="flex items-center gap-2">
                        <User className="h-5 w-5 text-slate-400" />
                        <span className="font-medium text-white">
                          {doc.user.firstName} {doc.user.lastName}
                        </span>
                      </div>
                      {getStatusBadge(doc.status)}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-slate-400 mb-4">
                      <div>
                        <span className="font-medium text-slate-300">Email:</span> {doc.user.email}
                      </div>
                      <div>
                        <span className="font-medium text-slate-300">User ID:</span> {doc.user.referralId}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium text-slate-300">Submitted:</span> {formatDate(doc.submittedAt)}
                      </div>
                    </div>

                    <div className="flex items-center gap-2 text-sm text-slate-400 mb-4">
                      <FileText className="h-4 w-4" />
                      <span className="font-medium text-slate-300">Document Type:</span> {doc.documentType}
                    </div>

                    {doc.rejectionReason && (
                      <div className="bg-red-900/20 border border-red-700 rounded-lg p-3 mb-4">
                        <div className="flex items-center gap-2 text-red-300 text-sm font-medium mb-1">
                          <X className="h-4 w-4" />
                          Rejection Reason
                        </div>
                        <p className="text-red-400 text-sm">{doc.rejectionReason}</p>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(doc.documentUrl, '_blank')}
                      className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>

                    {doc.status === 'PENDING' && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedDocument(doc);
                            setReviewAction('approve');
                          }}
                          className="border-green-600 text-green-400 hover:text-green-300 hover:bg-green-900/20"
                        >
                          <Check className="h-4 w-4 mr-1" />
                          Approve
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedDocument(doc);
                            setReviewAction('reject');
                          }}
                          className="border-red-600 text-red-400 hover:text-red-300 hover:bg-red-900/20"
                        >
                          <X className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Review Modal */}
      {selectedDocument && reviewAction && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50">
          <div className="bg-slate-800 border border-slate-700 rounded-xl max-w-md w-full p-6">
            <h3 className="text-lg font-semibold mb-4 text-white">
              {reviewAction === 'approve' ? 'Approve KYC Document' : 'Reject KYC Document'}
            </h3>

            <div className="mb-4">
              <p className="text-slate-400 mb-2">
                User: <span className="font-medium text-white">{selectedDocument.user.firstName} {selectedDocument.user.lastName}</span>
              </p>
              <p className="text-slate-400">
                Document: <span className="font-medium text-white">{selectedDocument.documentType}</span>
              </p>
            </div>

            {reviewAction === 'reject' && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Rejection Reason *
                </label>
                <textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  rows={3}
                  placeholder="Please provide a reason for rejection..."
                  required
                />
              </div>
            )}

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedDocument(null);
                  setReviewAction(null);
                  setRejectionReason('');
                }}
                disabled={processing}
                className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white"
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleReview(
                  selectedDocument.id,
                  reviewAction,
                  reviewAction === 'reject' ? rejectionReason : undefined
                )}
                disabled={processing || (reviewAction === 'reject' && !rejectionReason.trim())}
                loading={processing}
                className={reviewAction === 'approve'
                  ? 'bg-green-600 hover:bg-green-700 text-white'
                  : 'bg-red-600 hover:bg-red-700 text-white'
                }
              >
                {reviewAction === 'approve' ? 'Approve' : 'Reject'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
