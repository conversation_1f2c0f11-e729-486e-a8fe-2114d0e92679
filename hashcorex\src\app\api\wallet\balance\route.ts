import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { transactionDb } from '@/lib/database';

// GET - Fetch user's wallet balance
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Calculate wallet balance from completed transactions
    const transactions = await transactionDb.findByUserId(user.id);
    
    let balance = 0;
    
    for (const transaction of transactions) {
      if (transaction.status === 'COMPLETED') {
        switch (transaction.type) {
          case 'MINING_EARNINGS':
          case 'DIRECT_REFERRAL':
          case 'BINARY_BONUS':
          case 'DEPOSIT':
            balance += transaction.amount;
            break;
          case 'WITHDRAWAL':
          case 'PURCHASE':
            balance -= transaction.amount;
            break;
        }
      }
    }

    // Get pending earnings
    const pendingEarnings = transactions
      .filter(t => t.status === 'PENDING' && (
        t.type === 'MINING_EARNINGS' || 
        t.type === 'DIRECT_REFERRAL' || 
        t.type === 'BINARY_BONUS'
      ))
      .reduce((sum, t) => sum + t.amount, 0);

    // Get recent transactions (last 20)
    const recentTransactions = transactions
      .slice(0, 20)
      .map(t => ({
        id: t.id,
        type: t.type,
        amount: t.amount,
        description: t.description,
        status: t.status,
        createdAt: t.createdAt,
      }));

    return NextResponse.json({
      success: true,
      data: {
        balance: Math.max(0, balance), // Ensure balance is never negative
        pendingEarnings,
        recentTransactions,
      },
    });

  } catch (error: any) {
    console.error('Wallet balance fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch wallet balance' },
      { status: 500 }
    );
  }
}
