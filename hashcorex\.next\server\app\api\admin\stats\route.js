"use strict";(()=>{var e={};e.id=9759,e.ids=[9759],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},61166:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var r={};s.r(r),s.d(r,{GET:()=>p});var a=s(96559),n=s(48088),o=s(37719),u=s(32190),i=s(12909),c=s(31183);async function p(e){try{let{authenticated:t,user:s}=await (0,i.b9)(e);if(!t||!s)return u.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,i.qc)(s.id))return u.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let[r,a,n,o,p]=await Promise.all([c.z.$transaction([c.z.user.count(),c.z.user.count({where:{isActive:!0}})]),c.z.$transaction([c.z.user.count({where:{kycStatus:"PENDING"}}),c.z.user.count({where:{kycStatus:"APPROVED"}})]),c.z.$transaction([c.z.miningUnit.aggregate({_sum:{thsAmount:!0,investmentAmount:!0}}),c.z.miningUnit.aggregate({where:{status:"ACTIVE"},_sum:{thsAmount:!0}})]),c.z.$transaction([c.z.transaction.aggregate({where:{type:{in:["MINING_EARNINGS","DIRECT_REFERRAL","BINARY_BONUS"]},status:"COMPLETED"},_sum:{amount:!0}}),c.z.transaction.aggregate({where:{type:"PURCHASE",status:"COMPLETED"},_sum:{amount:!0}})]),c.z.$transaction([c.z.withdrawalRequest.count({where:{status:"PENDING"}}),c.z.withdrawalRequest.aggregate({where:{status:"COMPLETED"},_sum:{amount:!0}})])]),m=o[1]._sum.amount||0,d=new Date;d.setHours(0,0,0,0);let l=new Date(d);l.setDate(l.getDate()+1);let g=await c.z.transaction.aggregate({where:{type:"PURCHASE",status:"COMPLETED",createdAt:{gte:d,lt:l}},_sum:{amount:!0}}),h=.3*(g._sum.amount||0),w={totalUsers:r[0],activeUsers:r[1],pendingKYC:a[0],approvedKYC:a[1],totalInvestments:m,totalEarningsDistributed:o[0]._sum.amount||0,platformRevenue:.1*m,binaryPoolBalance:h,totalTHSSold:n[0]._sum.thsAmount||0,activeTHS:n[1]._sum.thsAmount||0,pendingWithdrawals:p[0],totalWithdrawals:p[1]._sum.amount||0};return u.NextResponse.json({success:!0,data:w})}catch(e){return console.error("Admin stats fetch error:",e),u.NextResponse.json({success:!1,error:"Failed to fetch admin statistics"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/stats/route",pathname:"/api/admin/stats",filename:"route",bundlePath:"app/api/admin/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\stats\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:g}=m;function h(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,5315,3529],()=>s(61166));module.exports=r})();