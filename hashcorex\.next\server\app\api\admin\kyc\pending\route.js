"use strict";(()=>{var e={};e.id=8897,e.ids=[8897],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6399:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p});var a=t(96559),n=t(48088),o=t(37719),i=t(32190),u=t(12909),d=t(31183);async function p(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return i.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(t.id))return i.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let s=await d.z.kYCDocument.findMany({where:{status:"PENDING"},include:{user:{select:{id:!0,email:!0,referralId:!0,createdAt:!0}}},orderBy:{createdAt:"asc"}}),a=new Map;for(let e of s){let r=e.userId;a.has(r)||a.set(r,{user:e.user,documents:[]}),a.get(r).documents.push({id:e.id,documentType:e.documentType,filePath:e.filePath,status:e.status,createdAt:e.createdAt})}let n=Array.from(a.values());return i.NextResponse.json({success:!0,data:n})}catch(e){return console.error("Pending KYC fetch error:",e),i.NextResponse.json({success:!1,error:"Failed to fetch pending KYC documents"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/kyc/pending/route",pathname:"/api/admin/kyc/pending",filename:"route",bundlePath:"app/api/admin/kyc/pending/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\pending\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:m}=c;function f(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3529],()=>t(6399));module.exports=s})();