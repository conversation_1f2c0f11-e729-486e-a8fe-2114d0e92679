"use strict";(()=>{var e={};e.id=9315,e.ids=[9315],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74621:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>A,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var r={};s.r(r),s.d(r,{GET:()=>p,POST:()=>c});var a=s(96559),o=s(48088),i=s(37719),n=s(32190),u=s(12909),d=s(6710);async function p(e){try{let t,{authenticated:s,user:r}=await (0,u.b9)(e);if(!s||!r)return n.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{searchParams:a}=new URL(e.url),o=parseInt(a.get("limit")||"50"),i=parseInt(a.get("offset")||"0"),p=a.get("status"),c=a.get("userId")||void 0;t=c?await d.J6.findByUserId(c,{status:p||void 0,limit:Math.min(o,100),offset:i}):await d.J6.findAll({status:p||void 0,limit:Math.min(o,100),offset:i});let l=await d.J6.getDepositStats();return n.NextResponse.json({success:!0,data:{deposits:t.map(e=>({id:e.id,userId:e.userId,user:e.user?{id:e.user.id,email:e.user.email,firstName:e.user.firstName,lastName:e.user.lastName}:null,transactionId:e.transactionId,amount:e.amount,usdtAmount:e.usdtAmount,tronAddress:e.tronAddress,senderAddress:e.senderAddress,status:e.status,blockNumber:e.blockNumber,blockTimestamp:e.blockTimestamp,confirmations:e.confirmations,verifiedAt:e.verifiedAt,processedAt:e.processedAt,failureReason:e.failureReason,createdAt:e.createdAt,updatedAt:e.updatedAt})),stats:{totalDeposits:l.totalDeposits,totalAmount:l.totalAmount,pendingDeposits:l.pendingDeposits},pagination:{limit:o,offset:i,hasMore:t.length===o},filters:{status:p,userId:c}}})}catch(e){return console.error("Admin deposits fetch error:",e),n.NextResponse.json({success:!1,error:"Failed to fetch deposits"},{status:500})}}async function c(e){try{let t,{authenticated:s,user:r}=await (0,u.b9)(e);if(!s||!r)return n.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{action:a,transactionId:o,reason:i}=await e.json();if(!a||!o)return n.NextResponse.json({success:!1,error:"Action and transaction ID are required"},{status:400});if(!await d.J6.findByTransactionId(o))return n.NextResponse.json({success:!1,error:"Deposit not found"},{status:404});switch(a){case"reject":if(!i)return n.NextResponse.json({success:!1,error:"Rejection reason is required"},{status:400});t=await d.J6.updateStatus(o,"REJECTED",{failureReason:i,processedAt:new Date});break;case"approve":t=await d.J6.updateStatus(o,"CONFIRMED",{verifiedAt:new Date});break;case"complete":t=await d.J6.markAsCompleted(o);break;default:return n.NextResponse.json({success:!1,error:"Invalid action"},{status:400})}return n.NextResponse.json({success:!0,message:`Deposit ${a}ed successfully`,data:t})}catch(e){return console.error("Admin deposit action error:",e),n.NextResponse.json({success:!1,error:"Failed to process deposit action"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/deposits/route",pathname:"/api/admin/deposits",filename:"route",bundlePath:"app/api/admin/deposits/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\deposits\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:x}=l;function A(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,5315,3529],()=>s(74621));module.exports=r})();