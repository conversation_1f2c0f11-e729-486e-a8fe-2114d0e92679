import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { systemLogDb } from '@/lib/database';
import { prisma } from '@/lib/prisma';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

// POST - Upload KYC document
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const documentType = formData.get('documentType') as string;

    // Validation
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!documentType || !['ID', 'SELFIE'].includes(documentType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid document type' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { success: false, error: 'Only image files are allowed' },
        { status: 400 }
      );
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { success: false, error: 'File size must be less than 5MB' },
        { status: 400 }
      );
    }

    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'kyc');
    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop();
    const fileName = `${user.id}_${documentType}_${uuidv4()}.${fileExtension}`;
    const filePath = join(uploadDir, fileName);
    const publicPath = `/uploads/kyc/${fileName}`;

    // Save file
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Check if document already exists and update or create
    const existingDocument = await prisma.kYCDocument.findFirst({
      where: {
        userId: user.id,
        documentType: documentType as 'ID' | 'SELFIE',
      },
    });

    let kycDocument;

    if (existingDocument) {
      // Update existing document
      kycDocument = await prisma.kYCDocument.update({
        where: { id: existingDocument.id },
        data: {
          filePath: publicPath,
          status: 'PENDING',
          reviewedAt: null,
          reviewedBy: null,
          rejectionReason: null,
        },
      });
    } else {
      // Create new document
      kycDocument = await prisma.kYCDocument.create({
        data: {
          userId: user.id,
          documentType: documentType as 'ID' | 'SELFIE',
          filePath: publicPath,
          status: 'PENDING',
        },
      });
    }

    // Check if user has uploaded both documents and update KYC status
    const userDocuments = await prisma.kYCDocument.findMany({
      where: { userId: user.id },
    });

    const hasIdDocument = userDocuments.some(doc => doc.documentType === 'ID');
    const hasSelfieDocument = userDocuments.some(doc => doc.documentType === 'SELFIE');

    if (hasIdDocument && hasSelfieDocument) {
      // Update user KYC status to PENDING if both documents are uploaded
      await prisma.user.update({
        where: { id: user.id },
        data: { kycStatus: 'PENDING' },
      });
    }

    // Log the upload
    await systemLogDb.create({
      action: 'KYC_DOCUMENT_UPLOADED',
      userId: user.id,
      details: {
        documentType,
        fileName,
        fileSize: file.size,
        documentId: kycDocument.id,
      },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json({
      success: true,
      message: 'Document uploaded successfully',
      data: {
        documentId: kycDocument.id,
        documentType: kycDocument.documentType,
        status: kycDocument.status,
      },
    });

  } catch (error: any) {
    console.error('KYC document upload error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to upload document' },
      { status: 500 }
    );
  }
}
