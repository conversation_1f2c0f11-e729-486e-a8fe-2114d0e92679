'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui';
import { Container, Grid, GridItem, Flex, PublicLayout } from '@/components/layout';
import { SolarPanel, MiningRig, Cryptocurrency, Leaf } from '@/components/icons';
import { ArrowRight, Shield, DollarSign, Users, Zap, Play, ChevronDown, Star, TrendingUp } from 'lucide-react';

export const HomePage: React.FC = () => {
  return (
    <PublicLayout>

      {/* Premium Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-yellow-50 opacity-30"></div>
        <div className="absolute inset-0 bg-white/95"></div>

        {/* Floating Elements */}
        <div className="absolute top-32 left-10 w-20 h-20 bg-yellow-400/20 rounded-full animate-float"></div>
        <div className="absolute top-48 right-20 w-16 h-16 bg-emerald-400/20 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-40 left-20 w-12 h-12 bg-slate-400/20 rounded-full animate-float" style={{animationDelay: '4s'}}></div>

        {/* Animated Icons */}
        <div className="absolute top-40 left-16 animate-float">
          <SolarPanel className="h-16 w-16 text-yellow-400/60" />
        </div>
        <div className="absolute top-60 right-24 animate-float" style={{animationDelay: '1s'}}>
          <MiningRig className="h-12 w-12 text-emerald-400/60" />
        </div>
        <div className="absolute bottom-32 left-1/4 animate-float" style={{animationDelay: '3s'}}>
          <Cryptocurrency className="h-14 w-14 text-slate-400/60" />
        </div>

        <Container className="relative z-10">
          <div className="text-center space-y-12 max-w-6xl mx-auto">
            {/* Main Heading */}
            <div className="space-y-6">
              <h1 className="text-6xl md:text-8xl lg:text-9xl font-black text-dark-900 leading-tight">
                <span className="block">Solar-Powered</span>
                <span className="block text-yellow-500">
                  Cloud Mining
                </span>
              </h1>
              <div className="w-40 h-2 bg-yellow-500 mx-auto rounded-full"></div>
            </div>

            {/* Premium Subtitle */}
            <p className="text-2xl md:text-3xl text-gray-700 max-w-5xl mx-auto leading-relaxed font-medium">
              Join the future of sustainable cryptocurrency mining with our
              <span className="text-emerald-600 font-bold"> eco-friendly</span>, solar-powered data centers.
              <span className="text-yellow-600 font-bold"> Earn daily returns</span> while supporting renewable energy.
            </p>

            {/* Premium CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-8 justify-center items-center pt-8">
              <Link href="/register">
                <Button variant="primary" size="xl" className="group relative overflow-hidden min-w-[280px]">
                  <span className="relative z-10 flex items-center gap-3">
                    <SolarPanel className="w-6 h-6" />
                    Start Mining Today
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </span>
                </Button>
              </Link>

              <Link href="/how-it-works">
                <Button variant="glass" size="xl" className="min-w-[280px]">
                  <Play className="w-5 h-5 mr-3" />
                  Watch Demo
                </Button>
              </Link>
            </div>

            {/* Trust Indicators */}
            <div className="pt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="space-y-3 p-6 glass-morphism rounded-2xl">
                <div className="text-4xl font-black text-solar-600">100%</div>
                <div className="text-sm text-gray-600 font-semibold">Solar Powered</div>
              </div>
              <div className="space-y-3 p-6 glass-morphism rounded-2xl">
                <div className="text-4xl font-black text-eco-600">24/7</div>
                <div className="text-sm text-gray-600 font-semibold">Mining Active</div>
              </div>
              <div className="space-y-3 p-6 glass-morphism rounded-2xl">
                <div className="text-4xl font-black text-purple-600">0.6-1.1%</div>
                <div className="text-sm text-gray-600 font-semibold">Daily ROI</div>
              </div>
              <div className="space-y-3 p-6 glass-morphism rounded-2xl">
                <div className="text-4xl font-black text-blue-600">$50</div>
                <div className="text-sm text-gray-600 font-semibold">Min Investment</div>
              </div>
            </div>
          </div>
        </Container>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ChevronDown className="w-8 h-8 text-gray-400" />
        </div>
      </section>

      {/* Premium Features Section */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white"></div>
        <Container className="relative z-10">
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-yellow-100 text-yellow-700 px-4 py-2 rounded-full text-sm font-semibold mb-6">
              <Star className="w-4 h-4" />
              Premium Features
            </div>
            <h2 className="text-5xl md:text-6xl font-black text-slate-900 mb-6">
              Why Choose{' '}
              <span className="text-yellow-500">
                HashCoreX?
              </span>
            </h2>
            <p className="text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Experience the perfect blend of profitability and sustainability
              with our cutting-edge mining platform.
            </p>
          </div>

          <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={8}>
            <div className="group text-center p-8 glass-morphism rounded-3xl hover:scale-105 transition-all duration-300 hover:shadow-2xl">
              <div className="relative inline-flex items-center justify-center w-20 h-20 bg-yellow-500 rounded-2xl mb-6 group-hover:scale-110 transition-transform">
                <Shield className="h-10 w-10 text-white" />
                <div className="absolute inset-0 bg-yellow-400 rounded-2xl animate-ping opacity-20"></div>
              </div>
              <h3 className="text-2xl font-bold text-slate-900 mb-4">KYC Verified</h3>
              <p className="text-gray-600 leading-relaxed">
                Secure and compliant platform with full KYC verification for all users.
              </p>
            </div>

            <div className="group text-center p-8 glass-morphism rounded-3xl hover:scale-105 transition-all duration-300 hover:shadow-2xl">
              <div className="relative inline-flex items-center justify-center w-20 h-20 bg-emerald-500 rounded-2xl mb-6 group-hover:scale-110 transition-transform">
                <DollarSign className="h-10 w-10 text-white" />
                <div className="absolute inset-0 bg-emerald-400 rounded-2xl animate-ping opacity-20"></div>
              </div>
              <h3 className="text-2xl font-bold text-slate-900 mb-4">USDT Payments</h3>
              <p className="text-gray-600 leading-relaxed">
                Easy deposits and withdrawals using USDT (TRC20) for global accessibility.
              </p>
            </div>

            <div className="group text-center p-8 glass-morphism rounded-3xl hover:scale-105 transition-all duration-300 hover:shadow-2xl">
              <div className="relative inline-flex items-center justify-center w-20 h-20 bg-slate-600 rounded-2xl mb-6 group-hover:scale-110 transition-transform">
                <TrendingUp className="h-10 w-10 text-white" />
                <div className="absolute inset-0 bg-slate-400 rounded-2xl animate-ping opacity-20"></div>
              </div>
              <h3 className="text-2xl font-bold text-slate-900 mb-4">Real ROI</h3>
              <p className="text-gray-600 leading-relaxed">
                Transparent daily returns with real mining operations and live statistics.
              </p>
            </div>

            <div className="group text-center p-8 glass-morphism rounded-3xl hover:scale-105 transition-all duration-300 hover:shadow-2xl">
              <div className="relative inline-flex items-center justify-center w-20 h-20 bg-emerald-600 rounded-2xl mb-6 group-hover:scale-110 transition-transform">
                <Leaf className="h-10 w-10 text-white" />
                <div className="absolute inset-0 bg-emerald-400 rounded-2xl animate-ping opacity-20"></div>
              </div>
              <h3 className="text-2xl font-bold text-slate-900 mb-4">Eco Mining</h3>
              <p className="text-gray-600 leading-relaxed">
                100% solar-powered mining operations supporting sustainable cryptocurrency.
              </p>
            </div>
          </Grid>
        </Container>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-dark-900 text-white">
        <Container>
          <Grid cols={{ default: 1, md: 3 }} gap={8}>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-yellow-400 mb-2">
                1,250+
              </div>
              <div className="text-xl text-gray-300">TH/s Sold</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-emerald-400 mb-2">
                5,000+
              </div>
              <div className="text-xl text-gray-300">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-yellow-400 mb-2">
                98.5%
              </div>
              <div className="text-xl text-gray-300">Uptime</div>
            </div>
          </Grid>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-yellow-500 text-white">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Ready to Start Mining?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Join thousands of investors earning daily returns with our 
              sustainable mining platform. Get started in minutes.
            </p>
            <Link href="/register">
              <Button 
                size="xl" 
                variant="secondary"
                className="bg-white text-dark-900 hover:bg-gray-100"
              >
                Create Account Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </Container>
      </section>

    </PublicLayout>
  );
};
