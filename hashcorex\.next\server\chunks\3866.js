exports.id=3866,exports.ids=[3866],exports.modules={2470:(e,t,r)=>{Promise.resolve().then(r.bind(r,57445))},4780:(e,t,r)=>{"use strict";r.d(t,{D1:()=>m,Oj:()=>h,Yq:()=>a,ZU:()=>x,ZV:()=>l,cn:()=>n,jI:()=>u,lW:()=>c,r6:()=>d,vv:()=>i});var s=r(49384),o=r(82348);function n(...e){return(0,o.QP)((0,s.$)(e))}function i(e,t="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}function l(e,t=2){return new Intl.NumberFormat("en-US",{minimumFractionDigits:t,maximumFractionDigits:t}).format(e)}function a(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function d(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}function c(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.focus(),t.select();try{return document.execCommand("copy"),Promise.resolve()}catch(e){return Promise.reject(e)}finally{document.body.removeChild(t)}}function h(e){let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t}}function u(e){return e>=1e3?`${(e/1e3).toFixed(1)}K TH/s`:`${e.toFixed(2)} TH/s`}function x(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+(6-e.getUTCDay())),t.setUTCHours(15,0,0,0),e>t&&t.setUTCDate(t.getUTCDate()+7);let r=t.getTime()-e.getTime(),s=Math.floor(r/864e5),o=Math.floor(r%864e5/36e5);return{days:s,hours:o,minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}}function m(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+1),t.setUTCHours(0,0,0,0);let r=t.getTime()-e.getTime(),s=Math.floor(r/36e5);return{hours:s,minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}}},26207:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},34014:(e,t,r)=>{Promise.resolve().then(r.bind(r,49567))},49567:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx","useAuth")},57445:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,AuthProvider:()=>i});var s=r(60687),o=r(43210);let n=(0,o.createContext)(void 0),i=({children:e})=>{let[t,r]=(0,o.useState)(null),[i,l]=(0,o.useState)(!0);(0,o.useEffect)(()=>{a()},[]);let a=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&r(t.data)}}catch(e){console.error("Auth check failed:",e)}finally{l(!1)}},d=async(e,t)=>{l(!0);try{let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),o=await s.json();if(!o.success)throw Error(o.error||"Login failed");r(o.data.user)}catch(e){throw e}finally{l(!1)}},c=async(e,t,r,s,o,n,i)=>{l(!0);try{let l=i?`/api/auth/register?side=${i}`:"/api/auth/register",a=await fetch(l,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:t,lastName:r,password:s,confirmPassword:o,referralCode:n})}),c=await a.json();if(!c.success)throw Error(c.error||"Registration failed");await d(e,s)}catch(e){throw e}finally{l(!1)}},h=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{r(null)}},u=async()=>{await a()};return(0,s.jsx)(n.Provider,{value:{user:t,loading:i,login:d,register:c,logout:h,refreshUser:u},children:e})},l=()=>{let e=(0,o.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},61135:()=>{},61212:(e,t,r)=>{"use strict";r.d(t,{$n:()=>d,Zp:()=>c,Wu:()=>x,aR:()=>h,ZB:()=>u,pd:()=>m,Rh:()=>w,aF:()=>g});var s=r(60687),o=r(43210),n=r.n(o),i=r(24224),l=r(4780);let a=(0,i.F)("inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl",{variants:{variant:{primary:"bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500",success:"bg-emerald-500 text-white hover:bg-emerald-600 focus:ring-emerald-500",danger:"bg-red-500 text-white hover:bg-red-600 focus:ring-red-500",outline:"border-2 border-yellow-500 bg-transparent text-yellow-600 hover:bg-yellow-500 hover:text-white focus:ring-yellow-500",ghost:"text-gray-600 hover:bg-yellow-50 hover:text-yellow-700 focus:ring-yellow-500 rounded-lg",link:"text-yellow-600 underline-offset-4 hover:underline focus:ring-yellow-500 hover:text-yellow-700",premium:"bg-slate-800 text-white hover:bg-slate-900 focus:ring-slate-500",glass:"glass-morphism text-slate-900 hover:bg-white/20 backdrop-blur-xl border border-white/20"},size:{sm:"h-10 px-4 text-sm rounded-lg",md:"h-12 px-6 text-base rounded-xl",lg:"h-14 px-8 text-lg rounded-xl",xl:"h-16 px-10 text-xl rounded-2xl font-bold",icon:"h-12 w-12 rounded-xl"}},defaultVariants:{variant:"primary",size:"md"}}),d=n().forwardRef(({className:e,variant:t,size:r,loading:o,leftIcon:n,rightIcon:i,children:d,disabled:c,...h},u)=>(0,s.jsxs)("button",{className:(0,l.cn)(a({variant:t,size:r,className:e})),ref:u,disabled:c||o,...h,children:[o&&(0,s.jsx)("div",{className:"mr-2",children:(0,s.jsx)("div",{className:"spinner"})}),n&&!o&&(0,s.jsx)("span",{className:"mr-2",children:n}),d,i&&!o&&(0,s.jsx)("span",{className:"ml-2",children:i})]}));d.displayName="Button";let c=n().forwardRef(({className:e,children:t,...r},o)=>(0,s.jsx)("div",{ref:o,className:(0,l.cn)("rounded-2xl border border-gray-200/50 bg-white/90 backdrop-blur-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] overflow-hidden",e),...r,children:t}));c.displayName="Card";let h=n().forwardRef(({className:e,children:t,...r},o)=>(0,s.jsx)("div",{ref:o,className:(0,l.cn)("flex flex-col space-y-1.5 p-6 pb-4",e),...r,children:t}));h.displayName="CardHeader";let u=n().forwardRef(({className:e,children:t,...r},o)=>(0,s.jsx)("h3",{ref:o,className:(0,l.cn)("text-xl font-semibold leading-none tracking-tight text-dark-900",e),...r,children:t}));u.displayName="CardTitle",n().forwardRef(({className:e,children:t,...r},o)=>(0,s.jsx)("p",{ref:o,className:(0,l.cn)("text-sm text-gray-500",e),...r,children:t})).displayName="CardDescription";let x=n().forwardRef(({className:e,children:t,...r},o)=>(0,s.jsx)("div",{ref:o,className:(0,l.cn)("p-6 pt-0",e),...r,children:t}));x.displayName="CardContent",n().forwardRef(({className:e,children:t,...r},o)=>(0,s.jsx)("div",{ref:o,className:(0,l.cn)("flex items-center p-6 pt-0",e),...r,children:t})).displayName="CardFooter";let m=n().forwardRef(({className:e,type:t,label:r,error:o,leftIcon:n,rightIcon:i,...a},d)=>(0,s.jsxs)("div",{className:"w-full",children:[r&&(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-700 mb-2",children:r}),(0,s.jsxs)("div",{className:"relative",children:[n&&(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("span",{className:"text-gray-400",children:n})}),(0,s.jsx)("input",{type:t,className:(0,l.cn)("flex h-14 w-full rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm px-4 py-4 text-base shadow-inner focus:shadow-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-solar-500 focus:border-solar-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-gray-300",n&&"pl-12",i&&"pr-12",o&&"border-red-500 focus:ring-red-500 focus:border-red-500",e),ref:d,...a}),i&&(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,s.jsx)("span",{className:"text-gray-400",children:i})})]}),o&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o})]}));m.displayName="Input";var f=r(51215),y=r(11860);let g=({isOpen:e,onClose:t,title:r,children:n,size:i="md",showCloseButton:a=!0})=>{if((0,o.useEffect)(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]),(0,o.useEffect)(()=>{let r=e=>{"Escape"===e.key&&t()};return e&&document.addEventListener("keydown",r),()=>{document.removeEventListener("keydown",r)}},[e,t]),!e)return null;let c=(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t}),(0,s.jsxs)("div",{className:(0,l.cn)("relative w-full bg-white rounded-xl shadow-xl transform transition-all",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[i]),onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-dark-900",children:r}),a&&(0,s.jsx)(d,{variant:"ghost",size:"icon",onClick:t,className:"h-8 w-8 rounded-full",children:(0,s.jsx)(y.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"p-6",children:n})]})]});return(0,f.createPortal)(c,document.body)},w=({size:e="md",className:t,text:r})=>(0,s.jsxs)("div",{className:(0,l.cn)("flex flex-col items-center justify-center",t),children:[(0,s.jsx)("div",{className:(0,l.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-solar-500",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[e])}),r&&(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:r})]})},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71180:(e,t,r)=>{"use strict";r.d(t,{Lc:()=>i,hK:()=>l,NC:()=>n,MX:()=>o,Kj:()=>a});var s=r(60687);r(43210);let o=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("line",{x1:"2",y1:"8",x2:"22",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"2",y1:"12",x2:"22",y2:"12",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"2",y1:"16",x2:"22",y2:"16",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"6",y1:"4",x2:"6",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"10",y1:"4",x2:"10",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"14",y1:"4",x2:"14",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"18",y1:"4",x2:"18",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("circle",{cx:"20",cy:"2",r:"1",fill:"currentColor"}),(0,s.jsx)("path",{d:"M19 1l1 1-1 1-1-1z",fill:"currentColor"})]}),n=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("rect",{x:"2",y:"6",width:"20",height:"12",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("rect",{x:"4",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"8",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"12",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"16",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"4",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"8",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"12",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"16",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("circle",{cx:"20",cy:"4",r:"1",fill:"currentColor"}),(0,s.jsx)("line",{x1:"20",y1:"4",x2:"20",y2:"6",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"18",y1:"2",x2:"22",y2:"2",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"19",y1:"1",x2:"21",y2:"3",stroke:"currentColor",strokeWidth:"1"}),(0,s.jsx)("line",{x1:"21",y1:"1",x2:"19",y2:"3",stroke:"currentColor",strokeWidth:"1"})]}),i=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M8 12h8M12 8v8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{d:"M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("line",{x1:"12",y1:"6",x2:"12",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"12",y1:"16",x2:"12",y2:"18",stroke:"currentColor",strokeWidth:"2"})]}),l=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("path",{d:"M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z",fill:"currentColor"})]}),a=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("line",{x1:"12",y1:"12",x2:"12",y2:"22",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{d:"M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("circle",{cx:"12",cy:"12",r:"1",fill:"currentColor"}),(0,s.jsx)("line",{x1:"10",y1:"22",x2:"14",y2:"22",stroke:"currentColor",strokeWidth:"2"})]})},86455:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>l});var s=r(37413),o=r(25091),n=r.n(o);r(61135);var i=r(49567);let l={title:"HashCoreX - Solar-Powered Cloud Mining",description:"Sustainable cryptocurrency mining with solar energy. Earn daily ROI with our eco-friendly mining platform."};function a({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().variable} font-sans antialiased`,children:(0,s.jsx)(i.AuthProvider,{children:e})})})}}};