"use strict";(()=>{var e={};e.id=218,e.ids=[218],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8023:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>g,routeModule:()=>f,serverHooks:()=>E,workAsyncStorage:()=>T,workUnitAsyncStorage:()=>A});var r={};s.r(r),s.d(r,{GET:()=>p,POST:()=>l,PUT:()=>m});var a=s(96559),o=s(48088),n=s(37719),i=s(32190),u=s(12909),d=s(6710),c=s(59480);async function p(e){try{let{authenticated:t,user:s}=await (0,u.b9)(e);if(!t||!s)return i.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(s.id))return i.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let r=await d.rs.get("USDT_DEPOSIT_ADDRESS"),a=await d.rs.get("MIN_DEPOSIT_AMOUNT"),o=await d.rs.get("MAX_DEPOSIT_AMOUNT"),n=await d.rs.get("DEPOSIT_ENABLED"),c=await d.rs.get("MIN_CONFIRMATIONS"),p=await d.rs.get("DEPOSIT_FEE_PERCENTAGE");return i.NextResponse.json({success:!0,data:{depositAddress:r||"",minDepositAmount:parseFloat(a||"10"),maxDepositAmount:parseFloat(o||"10000"),depositEnabled:"true"===n,minConfirmations:parseInt(c||"1"),depositFeePercentage:parseFloat(p||"0")}})}catch(e){return console.error("Deposit settings fetch error:",e),i.NextResponse.json({success:!1,error:"Failed to fetch deposit settings"},{status:500})}}async function l(e){try{let{authenticated:t,user:s}=await (0,u.b9)(e);if(!t||!s)return i.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(s.id))return i.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{depositAddress:r,minDepositAmount:a,maxDepositAmount:o,depositEnabled:n,minConfirmations:p,depositFeePercentage:l}=await e.json(),m=[];if(r&&!(0,c.af)(r)&&m.push("Invalid USDT TRC20 address format"),void 0!==a&&(a<0||a>1e6)&&m.push("Minimum deposit amount must be between 0 and 1,000,000"),void 0!==o&&(o<0||o>1e6)&&m.push("Maximum deposit amount must be between 0 and 1,000,000"),void 0!==a&&void 0!==o&&a>o&&m.push("Minimum deposit amount cannot be greater than maximum deposit amount"),void 0!==p&&(p<0||p>100)&&m.push("Minimum confirmations must be between 0 and 100"),void 0!==l&&(l<0||l>50)&&m.push("Deposit fee percentage must be between 0 and 50"),m.length>0)return i.NextResponse.json({success:!1,error:m.join(", ")},{status:400});let f=[];return void 0!==r&&(await d.rs.set("USDT_DEPOSIT_ADDRESS",r),f.push({key:"USDT_DEPOSIT_ADDRESS",value:r})),void 0!==a&&(await d.rs.set("MIN_DEPOSIT_AMOUNT",a.toString()),f.push({key:"MIN_DEPOSIT_AMOUNT",value:a.toString()})),void 0!==o&&(await d.rs.set("MAX_DEPOSIT_AMOUNT",o.toString()),f.push({key:"MAX_DEPOSIT_AMOUNT",value:o.toString()})),void 0!==n&&(await d.rs.set("DEPOSIT_ENABLED",n.toString()),f.push({key:"DEPOSIT_ENABLED",value:n.toString()})),void 0!==p&&(await d.rs.set("MIN_CONFIRMATIONS",p.toString()),f.push({key:"MIN_CONFIRMATIONS",value:p.toString()})),void 0!==l&&(await d.rs.set("DEPOSIT_FEE_PERCENTAGE",l.toString()),f.push({key:"DEPOSIT_FEE_PERCENTAGE",value:l.toString()})),await d.AJ.create({action:"DEPOSIT_SETTINGS_UPDATED",adminId:s.id,details:{updates:f},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),i.NextResponse.json({success:!0,message:"Deposit settings updated successfully"})}catch(e){return console.error("Deposit settings update error:",e),i.NextResponse.json({success:!1,error:"Failed to update deposit settings"},{status:500})}}async function m(e){try{let{authenticated:t,user:s}=await (0,u.b9)(e);if(!t||!s)return i.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(s.id))return i.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let r=[{key:"USDT_DEPOSIT_ADDRESS",value:""},{key:"MIN_DEPOSIT_AMOUNT",value:"10"},{key:"MAX_DEPOSIT_AMOUNT",value:"10000"},{key:"DEPOSIT_ENABLED",value:"true"},{key:"MIN_CONFIRMATIONS",value:"1"},{key:"DEPOSIT_FEE_PERCENTAGE",value:"0"}];for(let e of r)await d.rs.set(e.key,e.value);return await d.AJ.create({action:"DEPOSIT_SETTINGS_RESET",adminId:s.id,details:{defaultSettings:r},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),i.NextResponse.json({success:!0,message:"Deposit settings reset to defaults"})}catch(e){return console.error("Deposit settings reset error:",e),i.NextResponse.json({success:!1,error:"Failed to reset deposit settings"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/deposit-settings/route",pathname:"/api/admin/deposit-settings",filename:"route",bundlePath:"app/api/admin/deposit-settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\deposit-settings\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:T,workUnitAsyncStorage:A,serverHooks:E}=f;function g(){return(0,n.patchFetch)({workAsyncStorage:T,workUnitAsyncStorage:A})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},59480:(e,t,s)=>{s.d(t,{TA:()=>l,af:()=>m,gp:()=>p});let r=process.env.TRONGRID_API_KEY,a="TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",o=0;async function n(){let e=Date.now()-o;if(e<1e3){let t=1e3-e;await new Promise(e=>setTimeout(e,t))}o=Date.now()}async function i(e){await n();let t={"Content-Type":"application/json"};r&&(t["TRON-PRO-API-KEY"]=r);let s=await fetch(`https://api.trongrid.io${e}`,{method:"GET",headers:t});if(!s.ok)throw Error(`Trongrid API error: ${s.status} ${s.statusText}`);return await s.json()}async function u(e){try{let t=await i(`/v1/transactions/${e}`);return t.data&&t.data.length>0?t.data[0]:null}catch(e){return console.error("Error fetching transaction:",e),null}}async function d(e){try{let t=await i(`/wallet/gettransactioninfobyid?value=${e}`);return t.id?t:null}catch(e){return console.error("Error fetching transaction info:",e),null}}function c(e){return"T"+(e.startsWith("0x")?e.slice(2):e).substring(0,33)}async function p(e,t,s=1){try{if(!await u(e))return{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:"",blockNumber:0,blockTimestamp:0,confirmations:0,transactionId:e};let r=await d(e);if(!r)return{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:"",blockNumber:0,blockTimestamp:0,confirmations:0,transactionId:e};if("SUCCESS"!==r.receipt.result)return{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:a,blockNumber:r.blockNumber,blockTimestamp:r.blockTimeStamp,confirmations:0,transactionId:e};let o=function(e){let t=e.find(e=>e.address.toLowerCase()===a.toLowerCase()&&e.topics.length>=3&&"0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"===e.topics[0]);if(!t)return null;try{var s;let e=c(t.topics[1].slice(26)),r=c(t.topics[2].slice(26));return{amount:(s=t.data,parseInt(s,16)/1e6),fromAddress:e,toAddress:r}}catch(e){return console.error("Error parsing USDT transfer:",e),null}}(r.log||[]);if(!o)return{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:a,blockNumber:r.blockNumber,blockTimestamp:r.blockTimeStamp,confirmations:0,transactionId:e};let n=Date.now(),i=r.blockTimeStamp,p=Math.floor((n-i)/3e3);return{isValid:o.toAddress.toLowerCase()===t.toLowerCase()&&p>=s&&o.amount>0,amount:o.amount,fromAddress:o.fromAddress,toAddress:o.toAddress,contractAddress:a,blockNumber:r.blockNumber,blockTimestamp:r.blockTimeStamp,confirmations:Math.max(0,p),transactionId:e}}catch(t){return console.error("Error verifying USDT transaction:",t),{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:"",blockNumber:0,blockTimestamp:0,confirmations:0,transactionId:e}}}function l(e){return/^[a-fA-F0-9]{64}$/.test(e)}function m(e){return/^T[A-Za-z1-9]{33}$/.test(e)}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,5315,3529],()=>s(8023));module.exports=r})();