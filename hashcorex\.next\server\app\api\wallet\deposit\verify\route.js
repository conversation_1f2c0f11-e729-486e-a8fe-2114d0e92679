"use strict";(()=>{var e={};e.id=9530,e.ids=[9530],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45492:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>b,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>A});var s={};r.r(s),r.d(s,{POST:()=>l});var a=r(96559),o=r(48088),n=r(37719),i=r(32190),d=r(12909),u=r(6710),c=r(59480);let m=new Map;async function l(e){try{let{authenticated:t,user:r}=await (0,d.b9)(e);if(!t||!r)return i.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!function(e){let t=Date.now(),r=m.get(e);return!r||t>r.resetTime?(m.set(e,{count:1,resetTime:t+6e4}),!0):!(r.count>=5)&&(r.count++,!0)}(r.id))return i.NextResponse.json({success:!1,error:"Too many verification requests. Please wait before trying again."},{status:429});let{transactionId:s}=await e.json();if(!s||"string"!=typeof s)return i.NextResponse.json({success:!1,error:"Transaction ID is required"},{status:400});if(!(0,c.TA)(s))return i.NextResponse.json({success:!1,error:"Invalid Tron transaction ID format"},{status:400});let a=await u.J6.findByTransactionId(s);if(a)return i.NextResponse.json({success:!1,error:"Transaction already processed",data:{status:a.status,amount:a.usdtAmount,createdAt:a.createdAt}},{status:400});let o=await u.rs.get("USDT_DEPOSIT_ADDRESS"),n=parseFloat(await u.rs.get("MIN_DEPOSIT_AMOUNT")||"10"),l=parseFloat(await u.rs.get("MAX_DEPOSIT_AMOUNT")||"10000"),p=await u.rs.get("DEPOSIT_ENABLED")==="true",f=parseInt(await u.rs.get("MIN_CONFIRMATIONS")||"1");if(!p)return i.NextResponse.json({success:!1,error:"Deposits are currently disabled"},{status:503});if(!o)return i.NextResponse.json({success:!1,error:"Deposit address not configured. Please contact support."},{status:503});await u.AJ.create({action:"DEPOSIT_VERIFICATION_ATTEMPT",userId:r.id,details:{transactionId:s},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"});let A=await (0,c.gp)(s,o,f);if(!A.isValid)return await u.J6.create({userId:r.id,transactionId:s,amount:0,usdtAmount:A.amount,tronAddress:o,senderAddress:A.fromAddress,blockNumber:A.blockNumber.toString(),blockTimestamp:new Date(A.blockTimestamp),confirmations:A.confirmations}),await u.J6.markAsFailed(s,"Transaction verification failed: Invalid transaction, insufficient confirmations, or wrong recipient address"),i.NextResponse.json({success:!1,error:"Transaction verification failed",details:{isValid:A.isValid,amount:A.amount,confirmations:A.confirmations,minConfirmations:f,toAddress:A.toAddress,expectedAddress:o}},{status:400});if(A.amount<n)return await u.J6.create({userId:r.id,transactionId:s,amount:A.amount,usdtAmount:A.amount,tronAddress:o,senderAddress:A.fromAddress,blockNumber:A.blockNumber.toString(),blockTimestamp:new Date(A.blockTimestamp),confirmations:A.confirmations}),await u.J6.markAsFailed(s,`Deposit amount ${A.amount} USDT is below minimum ${n} USDT`),i.NextResponse.json({success:!1,error:`Deposit amount must be at least ${n} USDT`,data:{amount:A.amount,minAmount:n}},{status:400});if(A.amount>l)return await u.J6.create({userId:r.id,transactionId:s,amount:A.amount,usdtAmount:A.amount,tronAddress:o,senderAddress:A.fromAddress,blockNumber:A.blockNumber.toString(),blockTimestamp:new Date(A.blockTimestamp),confirmations:A.confirmations}),await u.J6.markAsFailed(s,`Deposit amount ${A.amount} USDT exceeds maximum ${l} USDT`),i.NextResponse.json({success:!1,error:`Deposit amount cannot exceed ${l} USDT`,data:{amount:A.amount,maxAmount:l}},{status:400});return await u.J6.create({userId:r.id,transactionId:s,amount:A.amount,usdtAmount:A.amount,tronAddress:o,senderAddress:A.fromAddress,blockNumber:A.blockNumber.toString(),blockTimestamp:new Date(A.blockTimestamp),confirmations:A.confirmations}),await u.J6.updateStatus(s,"CONFIRMED",{verifiedAt:new Date}),await u.k_.addDeposit(r.id,A.amount),await u.DR.create({userId:r.id,type:"DEPOSIT",amount:A.amount,description:`USDT TRC20 Deposit - TX: ${s}`,status:"COMPLETED"}),await u.J6.markAsCompleted(s),await u.AJ.create({action:"DEPOSIT_COMPLETED",userId:r.id,details:{transactionId:s,amount:A.amount,senderAddress:A.fromAddress,blockNumber:A.blockNumber},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),i.NextResponse.json({success:!0,message:"Deposit verified and credited successfully",data:{transactionId:s,amount:A.amount,status:"COMPLETED",confirmations:A.confirmations,blockNumber:A.blockNumber,processedAt:new Date}})}catch(t){if(console.error("Deposit verification error:",t),e.headers.get("authorization"))try{let{user:r}=await (0,d.b9)(e);r&&await u.AJ.create({action:"DEPOSIT_VERIFICATION_ERROR",userId:r.id,details:{error:t instanceof Error?t.message:"Unknown error"},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"})}catch(e){console.error("Failed to log error:",e)}return i.NextResponse.json({success:!1,error:"Failed to verify deposit. Please try again later."},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/wallet/deposit/verify/route",pathname:"/api/wallet/deposit/verify",filename:"route",bundlePath:"app/api/wallet/deposit/verify/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\deposit\\verify\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:A,serverHooks:b}=p;function w(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:A})}},55511:e=>{e.exports=require("crypto")},59480:(e,t,r)=>{r.d(t,{TA:()=>l,af:()=>p,gp:()=>m});let s=process.env.TRONGRID_API_KEY,a="TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",o=0;async function n(){let e=Date.now()-o;if(e<1e3){let t=1e3-e;await new Promise(e=>setTimeout(e,t))}o=Date.now()}async function i(e){await n();let t={"Content-Type":"application/json"};s&&(t["TRON-PRO-API-KEY"]=s);let r=await fetch(`https://api.trongrid.io${e}`,{method:"GET",headers:t});if(!r.ok)throw Error(`Trongrid API error: ${r.status} ${r.statusText}`);return await r.json()}async function d(e){try{let t=await i(`/v1/transactions/${e}`);return t.data&&t.data.length>0?t.data[0]:null}catch(e){return console.error("Error fetching transaction:",e),null}}async function u(e){try{let t=await i(`/wallet/gettransactioninfobyid?value=${e}`);return t.id?t:null}catch(e){return console.error("Error fetching transaction info:",e),null}}function c(e){return"T"+(e.startsWith("0x")?e.slice(2):e).substring(0,33)}async function m(e,t,r=1){try{if(!await d(e))return{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:"",blockNumber:0,blockTimestamp:0,confirmations:0,transactionId:e};let s=await u(e);if(!s)return{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:"",blockNumber:0,blockTimestamp:0,confirmations:0,transactionId:e};if("SUCCESS"!==s.receipt.result)return{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:a,blockNumber:s.blockNumber,blockTimestamp:s.blockTimeStamp,confirmations:0,transactionId:e};let o=function(e){let t=e.find(e=>e.address.toLowerCase()===a.toLowerCase()&&e.topics.length>=3&&"0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"===e.topics[0]);if(!t)return null;try{var r;let e=c(t.topics[1].slice(26)),s=c(t.topics[2].slice(26));return{amount:(r=t.data,parseInt(r,16)/1e6),fromAddress:e,toAddress:s}}catch(e){return console.error("Error parsing USDT transfer:",e),null}}(s.log||[]);if(!o)return{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:a,blockNumber:s.blockNumber,blockTimestamp:s.blockTimeStamp,confirmations:0,transactionId:e};let n=Date.now(),i=s.blockTimeStamp,m=Math.floor((n-i)/3e3);return{isValid:o.toAddress.toLowerCase()===t.toLowerCase()&&m>=r&&o.amount>0,amount:o.amount,fromAddress:o.fromAddress,toAddress:o.toAddress,contractAddress:a,blockNumber:s.blockNumber,blockTimestamp:s.blockTimeStamp,confirmations:Math.max(0,m),transactionId:e}}catch(t){return console.error("Error verifying USDT transaction:",t),{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:"",blockNumber:0,blockTimestamp:0,confirmations:0,transactionId:e}}}function l(e){return/^[a-fA-F0-9]{64}$/.test(e)}function p(e){return/^T[A-Za-z1-9]{33}$/.test(e)}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3529],()=>r(45492));module.exports=s})();