"use strict";(()=>{var e={};e.id=7028,e.ids=[7028],e.modules={775:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>c});var a=t(96559),n=t(48088),o=t(37719),i=t(32190),u=t(12909),p=t(2746);async function c(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return i.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{searchParams:s}=new URL(e.url),a=s.get("type")||"all",n=s.get("generation"),o={};switch(a){case"team":o.teamStats=await (0,p.fB)(t.id);break;case"health":o.treeHealth=await (0,p.g5)(t.id);break;case"generation":if(!n)return i.NextResponse.json({success:!1,error:"Generation parameter is required"},{status:400});{let e=parseInt(n);if(!(e>0)||!(e<=10))return i.NextResponse.json({success:!1,error:"Generation must be between 1 and 10"},{status:400});o.generationUsers=await (0,p.oS)(t.id,e)}break;case"counts":o.teamCounts=await (0,p.l6)(t.id);break;default:o.teamStats=await (0,p.fB)(t.id),o.treeHealth=await (0,p.g5)(t.id),o.teamCounts=await (0,p.l6)(t.id)}return i.NextResponse.json({success:!0,data:o})}catch(e){return console.error("Referral stats fetch error:",e),i.NextResponse.json({success:!1,error:"Failed to fetch referral statistics"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/referrals/stats/route",pathname:"/api/referrals/stats",filename:"route",bundlePath:"app/api/referrals/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\referrals\\stats\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:f}=d;function h(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3529,2746],()=>t(775));module.exports=s})();