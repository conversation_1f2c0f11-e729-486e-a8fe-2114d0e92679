'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { AdminDashboard } from '@/components/admin/AdminDashboard';
import { UserManagement } from '@/components/admin/UserManagement';
import { KYCReview } from '@/components/admin/KYCReview';
import { WithdrawalManagement } from '@/components/admin/WithdrawalManagement';
import { SystemSettings } from '@/components/admin/SystemSettings';
import { SystemLogs } from '@/components/admin/SystemLogs';
import { Loading } from '@/components/ui';

export default function AdminPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isAdmin, setIsAdmin] = useState(false);
  const [checkingAdmin, setCheckingAdmin] = useState(true);

  // Check if user is admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!loading && user) {
        try {
          const response = await fetch('/api/admin/check', {
            credentials: 'include',
          });
          
          if (response.ok) {
            const data = await response.json();
            setIsAdmin(data.isAdmin);
          } else {
            setIsAdmin(false);
          }
        } catch (error) {
          console.error('Error checking admin status:', error);
          setIsAdmin(false);
        }
      }
      setCheckingAdmin(false);
    };

    checkAdminStatus();
  }, [user, loading]);

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  // Redirect if not admin
  useEffect(() => {
    if (!checkingAdmin && !isAdmin && user) {
      router.push('/dashboard');
    }
  }, [isAdmin, checkingAdmin, user, router]);

  if (loading || checkingAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Loading admin panel..." />
      </div>
    );
  }

  if (!user || !isAdmin) {
    return null;
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <AdminDashboard />;
      case 'users':
        return <UserManagement />;
      case 'kyc':
        return <KYCReview />;
      case 'withdrawals':
        return <WithdrawalManagement />;
      case 'settings':
        return <SystemSettings />;
      case 'logs':
        return <SystemLogs />;
      default:
        return <AdminDashboard />;
    }
  };

  return (
    <AdminLayout activeTab={activeTab} onTabChange={setActiveTab}>
      {renderTabContent()}
    </AdminLayout>
  );
}
