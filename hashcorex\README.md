# HashCoreX - Solar-Powered Cloud Mining Platform

HashCoreX is a comprehensive cloud mining platform that combines sustainable solar energy with cryptocurrency mining, featuring a sophisticated binary referral system and automated ROI distribution.

## 🌟 Features

### Core Platform
- **Solar-Powered Mining**: 100% renewable energy mining operations
- **TH/s Mining Units**: Purchase and manage mining power with real ROI
- **Automated Earnings**: Daily ROI calculation and weekly payouts
- **Internal Wallet**: USDT-based wallet system with withdrawal capabilities
- **KYC Verification**: Secure identity verification system

### Binary Referral System
- **Binary Tree Structure**: Left/right placement system
- **Direct Referral Bonus**: 10% commission on direct referrals
- **Binary Matching**: Daily matching with up to 2,000 points per side
- **Binary Pool**: 30% of platform investments distributed daily

### Admin Panel
- **User Management**: Complete user administration
- **KYC Review**: Document review and approval system
- **Withdrawal Processing**: Manual withdrawal approval
- **System Settings**: Platform configuration management
- **Analytics Dashboard**: Comprehensive platform statistics

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 13+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd hashcorex
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup environment**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Setup database**
   ```bash
   npx prisma generate
   npx prisma db push
   npm run seed
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

Visit `http://localhost:3000` to access the platform.
