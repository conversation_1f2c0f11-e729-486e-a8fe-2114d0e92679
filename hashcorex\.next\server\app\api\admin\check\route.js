"use strict";(()=>{var e={};e.id=7718,e.ids=[7718],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},85111:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>u});var i=t(96559),a=t(48088),n=t(37719),o=t(32190),p=t(12909);async function u(e){try{let{authenticated:r,user:t}=await (0,p.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated",isAdmin:!1},{status:401});let s=await (0,p.qc)(t.id);return o.NextResponse.json({success:!0,isAdmin:s,user:{id:t.id,email:t.email}})}catch(e){return console.error("Admin check error:",e),o.NextResponse.json({success:!1,error:"Admin check failed",isAdmin:!1},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/check/route",pathname:"/api/admin/check",filename:"route",bundlePath:"app/api/admin/check/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\check\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:x,serverHooks:m}=c;function l(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:x})}},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3529],()=>t(85111));module.exports=s})();