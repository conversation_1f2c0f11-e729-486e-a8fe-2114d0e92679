import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Fetch pending KYC documents for admin review
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get pending KYC documents grouped by user
    const pendingDocuments = await prisma.kYCDocument.findMany({
      where: { status: 'PENDING' },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            referralId: true,
            createdAt: true,
          },
        },
      },
      orderBy: { createdAt: 'asc' },
    });

    // Group documents by user
    const userDocuments = new Map();
    
    for (const doc of pendingDocuments) {
      const userId = doc.userId;
      if (!userDocuments.has(userId)) {
        userDocuments.set(userId, {
          user: doc.user,
          documents: [],
        });
      }
      userDocuments.get(userId).documents.push({
        id: doc.id,
        documentType: doc.documentType,
        filePath: doc.filePath,
        status: doc.status,
        createdAt: doc.createdAt,
      });
    }

    const result = Array.from(userDocuments.values());

    return NextResponse.json({
      success: true,
      data: result,
    });

  } catch (error: any) {
    console.error('Pending KYC fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch pending KYC documents' },
      { status: 500 }
    );
  }
}
