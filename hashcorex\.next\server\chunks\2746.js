"use strict";exports.id=2746,exports.ids=[2746],exports.modules={2746:(e,r,t)=>{t.d(r,{E5:()=>p,OZ:()=>F,PM:()=>N,Yn:()=>z,fB:()=>R,g5:()=>A,gj:()=>E,l6:()=>T,oS:()=>S,placeUserInBinaryTree:()=>s,placeUserInSpecificSide:()=>u});var a=t(31183),i=t(6710);async function n(e,r){try{return(await m(e,r)).length}catch(e){return console.error("Downline count calculation error:",e),0}}async function l(e){try{let r=await n(e,"LEFT"),t=await n(e,"RIGHT"),a=r<=t?"LEFT":"RIGHT",l=await h(e,a);if(l)return l;let s=await h(e,"LEFT"===a?"RIGHT":"LEFT");if(s)return s;let d=await i.cc.findByReferrerId(e),o=d.some(e=>"LEFT"===e.placementSide),c=d.some(e=>"RIGHT"===e.placementSide);if(!o)return{userId:e,side:"LEFT"};if(!c)return{userId:e,side:"RIGHT"};return{userId:e,side:a}}catch(r){return console.error("Optimal placement position error:",r),{userId:e,side:"LEFT"}}}async function s(e,r){try{let t=await l(e);await i.cc.create({referrerId:t.userId,referredId:r,placementSide:t.side});let n="LEFT"===t.side?{leftReferralId:r}:{rightReferralId:r};return await a.z.user.update({where:{id:t.userId},data:n}),await d(e,r),await P(e,t.userId),t.side}catch(e){throw console.error("Binary tree placement error:",e),e}}async function d(e,r){try{await a.z.user.update({where:{id:r},data:{referrerId:e}}),await a.z.user.update({where:{id:e},data:{directReferralCount:{increment:1},updatedAt:new Date}}),await a.z.referral.updateMany({where:{referrerId:e,referredId:r},data:{isDirectSponsor:!0}})}catch(e){console.error("Sponsor relationship creation error:",e)}}async function o(e){try{let r=await n(e,"LEFT"),t=await n(e,"RIGHT");await a.z.user.update({where:{id:e},data:{totalLeftDownline:r,totalRightDownline:t,lastTreeUpdate:new Date}})}catch(e){console.error("Update cached downline counts error:",e)}}async function c(e){try{let r=await a.z.user.findUnique({where:{id:e},select:{totalLeftDownline:!0,totalRightDownline:!0,lastTreeUpdate:!0}});if(!r)return{left:0,right:0,total:0};if((r.lastTreeUpdate?Date.now()-r.lastTreeUpdate.getTime():1/0)<18e5&&null!==r.totalLeftDownline&&null!==r.totalRightDownline)return{left:r.totalLeftDownline,right:r.totalRightDownline,total:r.totalLeftDownline+r.totalRightDownline};{let r=await n(e,"LEFT"),t=await n(e,"RIGHT");return o(e).catch(console.error),{left:r,right:t,total:r+t}}}catch(e){return console.error("Get cached downline counts error:",e),{left:0,right:0,total:0}}}async function f(e,r){try{let t=await h(e,r);if(t)return t;let a=await m(e,r),l=e,s=1/0;for(let e of a){let r=await n(e.id,"LEFT"),t=await n(e.id,"RIGHT"),a=r+t;if(a<s){let r=await i.cc.findByReferrerId(e.id),t=r.some(e=>"LEFT"===e.placementSide),n=r.some(e=>"RIGHT"===e.placementSide);t&&n||(s=a,l=e.id)}}let d=await i.cc.findByReferrerId(l),o=d.some(e=>"LEFT"===e.placementSide),c=d.some(e=>"RIGHT"===e.placementSide);if(!o)return{userId:l,side:"LEFT"};if(!c)return{userId:l,side:"RIGHT"};let f=await n(l,"LEFT"),u=await n(l,"RIGHT");return{userId:l,side:f<=u?"LEFT":"RIGHT"}}catch(t){return console.error("Optimal placement in side error:",t),{userId:e,side:r}}}async function u(e,r,t){try{let n=await f(e,t);await i.cc.create({referrerId:n.userId,referredId:r,placementSide:n.side});let l="LEFT"===n.side?{leftReferralId:r}:{rightReferralId:r};return await a.z.user.update({where:{id:n.userId},data:l}),await d(e,r),await P(e,n.userId),n.side}catch(e){throw console.error("Specific side placement error:",e),e}}async function h(e,r){try{let t=(await i.cc.findByReferrerId(e)).find(e=>e.placementSide===r);if(!t)return{userId:e,side:r};let a=[t.referredId];for(;a.length>0;){let e=a.shift(),r=await i.cc.findByReferrerId(e),t=r.some(e=>"LEFT"===e.placementSide),n=r.some(e=>"RIGHT"===e.placementSide);if(!t)return{userId:e,side:"LEFT"};if(!n)return{userId:e,side:"RIGHT"};r.forEach(e=>{a.push(e.referredId)})}return null}catch(e){return console.error("Find available spot error:",e),null}}async function w(e){try{let r=[],t=e;for(let e=0;e<10;e++){let e=await a.z.referral.findFirst({where:{referredId:t},include:{referrer:{select:{id:!0,email:!0}}}});if(!e)break;r.push(e.referrer),t=e.referrerId}return r}catch(e){return console.error("Upline users fetch error:",e),[]}}async function m(e,r){try{let t=[],i=new Set,n=(await a.z.referral.findMany({where:{referrerId:e,placementSide:r},select:{referredId:!0}})).map(e=>e.referredId);for(;n.length>0;){let e=n.shift();if(!i.has(e))for(let r of(i.add(e),t.push({id:e}),await a.z.referral.findMany({where:{referrerId:e},select:{referredId:!0}})))i.has(r.referredId)||n.push(r.referredId)}return t}catch(e){return console.error("Downline users fetch error:",e),[]}}async function I(e){try{let r=[],t=new Set,i=(await a.z.referral.findMany({where:{referrerId:e},select:{referredId:!0}})).map(e=>e.referredId);for(;i.length>0;){let e=i.shift();if(t.has(e))continue;t.add(e);let n=await a.z.user.findUnique({where:{id:e},select:{id:!0,isActive:!0}});if(n)for(let l of(r.push({id:n.id,isActive:n.isActive}),await a.z.referral.findMany({where:{referrerId:e},select:{referredId:!0}})))t.has(l.referredId)||i.push(l.referredId)}return r}catch(e){return console.error("All downline users fetch error:",e),[]}}async function p(){try{console.log("Starting binary matching process...");let e=parseFloat(await i.rs.get("MAX_BINARY_POINTS_PER_SIDE")||"2000"),r=parseFloat(await i.rs.get("BINARY_POOL_PERCENTAGE")||"30"),t=((await a.z.miningUnit.aggregate({_sum:{investmentAmount:!0},where:{createdAt:{gte:new Date(Date.now()-864e5)}}}))._sum.investmentAmount||0)*r/100,n=await a.z.binaryPoints.findMany({where:{OR:[{leftPoints:{gt:0}},{rightPoints:{gt:0}}]},include:{user:{select:{id:!0,email:!0}}}});console.log(`Processing binary matching for ${n.length} users`);let l=0,s=[];for(let r of n)try{let n=Math.min(r.leftPoints,e),d=Math.min(r.rightPoints,e),o=Math.min(n,d);if(o>0){let e=o/Math.max(l,1)*t;e>0&&await i.DR.create({userId:r.userId,type:"BINARY_BONUS",amount:e,description:`Binary matching bonus - ${o} points matched`,status:"COMPLETED"}),await a.z.binaryPoints.update({where:{id:r.id},data:{leftPoints:Math.max(0,r.leftPoints-o),rightPoints:Math.max(0,r.rightPoints-o),matchedPoints:{increment:o},flushDate:new Date}}),s.push({userId:r.userId,matchedPoints:o,payout:e}),l+=o}}catch(e){console.error(`Error processing binary matching for user ${r.userId}:`,e)}return await i.AJ.create({action:"BINARY_MATCHING_PROCESSED",details:{usersProcessed:n.length,totalMatchedPoints:l,dailyBinaryPool:t,totalPayouts:s.reduce((e,r)=>e+r.payout,0),timestamp:new Date().toISOString()}}),console.log(`Binary matching completed. Processed ${s.length} users with ${l} total matched points.`),s}catch(e){throw console.error("Binary matching process error:",e),e}}async function y(e){try{let r=await a.z.user.findUnique({where:{id:e},select:{referrerId:!0}});if(!r?.referrerId)return null;return await a.z.user.findUnique({where:{id:r.referrerId},select:{id:!0,email:!0,firstName:!0,lastName:!0}})}catch(e){return console.error("Sponsor info fetch error:",e),null}}async function g(e){try{return await a.z.user.count({where:{referrerId:e}})}catch(e){return console.error("Direct referral count error:",e),0}}async function T(e){try{return await c(e)}catch(e){return console.error("Total team count error:",e),{left:0,right:0,total:0}}}async function R(e){try{let r=await a.z.user.findUnique({where:{id:e},select:{directReferralCount:!0}}),t=await c(e),i=(await I(e)).filter(e=>e.isActive).length,n=new Date(Date.now()-2592e6),l=await a.z.user.count({where:{referrerId:e,createdAt:{gte:n}}});return{directReferrals:r?.directReferralCount||0,leftTeam:t.left,rightTeam:t.right,totalTeam:t.total,activeMembers:i,recentJoins:l}}catch(e){return console.error("Detailed team stats error:",e),{directReferrals:0,leftTeam:0,rightTeam:0,totalTeam:0,activeMembers:0,recentJoins:0}}}async function S(e,r){try{if(r<=0)return[];let t=[{id:e,side:null}];for(let e=1;e<=r;e++){let e=[];for(let r of t)for(let t of(await a.z.referral.findMany({where:{referrerId:r.id},include:{referred:{select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0}}}})))e.push({id:t.referredId,side:t.placementSide});t=e}return(await Promise.all(t.map(async e=>({...await a.z.user.findUnique({where:{id:e.id},select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0}}),placementSide:e.side})))).filter(Boolean)}catch(e){return console.error("Users by generation error:",e),[]}}async function N(e,r=3,t=new Set){try{let n=async(e,r,l="")=>{if(r<=0)return null;let s=await a.z.user.findUnique({where:{id:e},select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0,isActive:!0}});if(!s)return null;let d=await y(e),o=await g(e),c=await T(e),f=await a.z.referral.findFirst({where:{referrerId:e,placementSide:"LEFT"},include:{referred:{select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0,isActive:!0}}}}),u=await a.z.referral.findFirst({where:{referrerId:e,placementSide:"RIGHT"},include:{referred:{select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0,isActive:!0}}}}),h=await i.FW.findByUserId(e),w=r>1&&(l.length<3||t.has(e)),m=null!==f,I=null!==u;return{user:s,sponsorInfo:d,directReferralCount:o,teamCounts:c,binaryPoints:h||{leftPoints:0,rightPoints:0,matchedPoints:0},hasLeftChild:m,hasRightChild:I,leftChild:w&&f?await n(f.referredId,r-1,l+"L"):null,rightChild:w&&u?await n(u.referredId,r-1,l+"R"):null}};return await n(e,r)}catch(e){throw console.error("Binary tree structure error:",e),e}}async function F(e){try{let r=await a.z.referral.findFirst({where:{referrerId:e,placementSide:"LEFT"},include:{referred:{select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0,isActive:!0}}}}),t=await a.z.referral.findFirst({where:{referrerId:e,placementSide:"RIGHT"},include:{referred:{select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0,isActive:!0}}}}),n=async e=>{if(!e)return null;let r=e.referredId,t=await y(r),n=await g(r),l=await T(r),s=await i.FW.findByUserId(r),d=await a.z.referral.findFirst({where:{referrerId:r,placementSide:"LEFT"},select:{id:!0}})!==null,o=await a.z.referral.findFirst({where:{referrerId:r,placementSide:"RIGHT"},select:{id:!0}})!==null;return{user:e.referred,sponsorInfo:t,directReferralCount:n,teamCounts:l,binaryPoints:s||{leftPoints:0,rightPoints:0,matchedPoints:0},hasLeftChild:d,hasRightChild:o,leftChild:null,rightChild:null}},l=await n(r),s=await n(t);return{leftChild:l,rightChild:s}}catch(e){return console.error("Load node children error:",e),{leftChild:null,rightChild:null}}}async function E(e,r,t=20){try{r.toLowerCase();let i=await m(e,"LEFT"),n=await m(e,"RIGHT"),l=[...i,...n].map(e=>e.id);if(0===l.length)return[];let s=await a.z.user.findMany({where:{id:{in:l},OR:[{email:{contains:r,mode:"insensitive"}},{firstName:{contains:r,mode:"insensitive"}},{lastName:{contains:r,mode:"insensitive"}}]},select:{id:!0,email:!0,firstName:!0,lastName:!0,createdAt:!0,referrerId:!0},take:t});return await Promise.all(s.map(async r=>{let t,i=await L(e,r.id),n=i.split("-").length;return r.referrerId&&(t=await a.z.user.findUnique({where:{id:r.referrerId},select:{id:!0,email:!0,firstName:!0,lastName:!0}})),{id:r.id,email:r.email,firstName:r.firstName,lastName:r.lastName,createdAt:r.createdAt,placementPath:i,generation:n,sponsorInfo:t||void 0}}))}catch(e){return console.error("Search users in tree error:",e),[]}}async function L(e,r){try{if(e===r)return"ROOT";let t=[],i=r;for(;i!==e;){let e=await a.z.referral.findFirst({where:{referredId:i}});if(!e||(t.unshift("LEFT"===e.placementSide?"L":"R"),i=e.referrerId,t.length>20))break}return t.join("-")||"UNKNOWN"}catch(e){return console.error("Get placement path error:",e),"UNKNOWN"}}async function P(e,r){try{e!==r&&await o(e),await o(r);let t=(await w(r)).map(e=>o(e.id));await Promise.all(t)}catch(e){console.error("Update tree counts after placement error:",e)}}async function z(e){try{let r=e.map(e=>o(e));await Promise.all(r)}catch(e){console.error("Bulk update tree counts error:",e)}}async function A(e){try{let r=await c(e),t=r.total,i=Math.min(r.left,r.right),n=Math.max(r.left,r.right),l=0,s=0,d=0,o=[{userId:e,depth:0}],f=new Set;for(;o.length>0;){let{userId:e,depth:r}=o.shift();if(!f.has(e))for(let t of(f.add(e),l=Math.max(l,r),s+=r,d++,await a.z.referral.findMany({where:{referrerId:e},select:{referredId:!0}})))f.has(t.referredId)||o.push({userId:t.referredId,depth:r+1})}let u=d>0?s/d:0,h=Math.pow(2,l+1)-1,w=Math.max(0,h-t);return{totalUsers:t,balanceRatio:n>0?i/n:1,averageDepth:u,maxDepth:l,emptyPositions:w}}catch(e){return console.error("Tree health stats error:",e),{totalUsers:0,balanceRatio:1,averageDepth:0,maxDepth:0,emptyPositions:0}}}}};